package com.voc.service.model.nodes;

import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.yomahub.liteflow.annotation.LiteflowComponent;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "emptyNode", name = "空节点")
public class EmptyNode extends AbstractNode {

    @Override
    public void process() throws Exception {
    }

}

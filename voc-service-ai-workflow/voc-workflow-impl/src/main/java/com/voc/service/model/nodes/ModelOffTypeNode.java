package com.voc.service.model.nodes;

import com.voc.service.model.enums.ModelTypeEnum;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;

@LiteflowComponent(id = "modelOffTypeNode", name = "离线模型类型选择")
public class ModelOffTypeNode extends AbstractNodeIf {

    @Override
    public boolean processIf() throws Exception {
        ThirdPartyAIContext context = this.getRequestData();
        Integer modelType = context.getModelType();
        return modelType.equals(ModelTypeEnum.AI_OFFLINE.getType());
    }
}

package com.voc.service.model.config;

import lombok.Data;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AnalysisConfig
 * @createTime 2024年03月08日 15:37
 * @Copyright cuick
 */
@Configuration
@SuppressWarnings("all")
@Data
@EnableScheduling
//@RefreshScope
public class AIWorkflowConfig {

    // 监控智库AI服务处理结果时间间隔
    @Value("${listening_ai_result_interval:10000}")
    int listeningAiResultInterval;

    //从智库AI服务推送数据结果数据到后端Normalizer流程推送数据集大小
    @Value("${push_normalizer_flow_dataset_size:1}")
    int pushNormalizerFlowDatasetSize;

    @Value("${push_analysis_service_mq_dataset_size:100}")
    int pushAnalysisServiceMqDatasetSize;

    //再接收到优先级高的topic数据时，暂停权重低的topic消费， 之后通过此参数判断最后一次消费数据的时间间隔
    //超出此时间范围时，将自动恢复权重低的topic消费
    @Value("${resume_consumption_interval:180}")
    int resumeConsumptionInterval;


    @Value("${opinion_score_threshold:0.90F}")
    float opinionScoreThreshold;

    @Value("${scenario_score_threshold:0.88F}")
    float scenarioScoreThreshold;

    @Value("${model.ai_workflow.normalizer.tag_mapping.db}")
    String tagMappingDb;

    @Getter
    @Value("${feign.default.token:eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoiYW5hbHlzaXNfYXBpIiwiaWRlbnRpdHlfdHlwZSI6ImJhc2UiLCJhcHBfaWQiOiJhbmFseXNpcyIsInVzZXJuYW1lIjoiY1Fkb094bmg2eVEwMW5lc2ZLTlhVNjFKQmx5RFg3dHc4YXhod0JjNVl4aXl1MC9CYjdDQWZwQjJ5QTFxYjQ4QiIsInN1YiI6ImFuYWx5c2lzX2FwaSIsImlhdCI6MTcxMDQxMTQyMSwiZXhwIjo0MDc1NjExNDIxfQ.G1kAeqwp0udBimnDdIAqL1nSIcgV0u6YrU0bb5OchJ0}")
    String defaultToken;

    @Value("${milvus.opinion_collectionName:opinion_v1_1}")
    String opinionCollectionName;
    @Value("${milvus.opinion_vectorFieldName:embedding}")
    String opinionVectorFieldName;
    @Value("#{'${milvus.opinion_fieldNames:id,opinion}'.split(',')}")
    List<String> opinionFieldNames;
    @Value("${milvus.scenario_collectionName:scenarios}")
    String scenarioCollectionName;
    @Value("${milvus.scenario_vectorFieldName:embedding}")
    String scenarioVectorFieldName;
    @Value("#{'${milvus.scenario_fieldNames:id,L4}'.split(',')}")
    List<String> scenarioFieldNames;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voc.service.security.impl.mapper.CredentialsMapper">

    <resultMap id="result" type="com.voc.service.security.impl.entity.CredentialsEntity">
        <id column="id" property="id"></id>
        <result column="user_id" property="userId"></result>
        <result column="identity_type" property="identityType"></result>
        <result column="identifier" property="identifier"></result>
        <result column="credential" property="credential"></result>
        <result column="non_expired" property="nonExpired"></result>
        <result column="non_locked" property="nonLocked"></result>
        <result column="enabled" property="enabled"></result>
        <result column="create_time" property="createTime"></result>
        <result column="update_time" property="updateTime"></result>
        <result column="start_expire_date" property="startExpireDate"></result>
        <result column="operator" property="operator"></result>
        <result column="admin" property="admin"></result>
    </resultMap>
    <select id="selectByUserIds"  resultMap="result">
        select
        *
        from
        (
            select
            id,
            user_id,
            app_id,
            identity_type,
            identifier,
            credential,
            enabled,
            non_locked,
            create_time,
            update_time,
            expire_Date,
            start_expire_date,
            operator,
            admin,
            if(expire_date &lt; CURDATE() ,
            0,
            1) non_expired
            from
            sys_credentials
            where user_id in
            <foreach collection="list" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="appId != null">
                and app_id = #{appId}
            </if>
        )ca
        left join (
            select
            credential_id,
            count(id) as login_counts,
            max(login_time) as last_login_time
            from
            sys_login_histroy
            where   user_id in
            <foreach collection="list" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            and app_id = #{appId}
            group by user_id
        )l on
        ca.id = l.credential_id
    </select>

    <delete id="removeTestUsers" parameterType="list">
        delete
        from sys_credentials
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

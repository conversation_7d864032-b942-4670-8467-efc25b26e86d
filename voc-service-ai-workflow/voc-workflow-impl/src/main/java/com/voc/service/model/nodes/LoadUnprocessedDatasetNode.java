package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollUtil;
import com.voc.service.model.api.IAnalysisService;
import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.config.AIWorkflowConfig;
import com.voc.service.model.model.MergingResultDataModel;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.AnalysisDataContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Title: Test
 * @Package: com.voc.service.model.nodes
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 15:04
 * @Version:1.0
 */
@LiteflowComponent(id = "loadUnprocessedDatasetNode", name = "加载未完成推送的数据集节点")
public class LoadUnprocessedDatasetNode extends AbstractNodeIf {
    private static final Logger log = LoggerFactory.getLogger(LoadUnprocessedDatasetNode.class);
    @Autowired
    AIWorkflowConfig config;
    @Autowired
    IAnalysisService analysisService;
    @Autowired
    IMergeStatusRecordService mergeStatusRecordService;

    @Override
    public boolean processIf() throws Exception {

        AnalysisDataContext context = this.getRequestData();
        List<MergingResultDataModel> list = mergeStatusRecordService.findUnpushedData(config.getPushAnalysisServiceMqDatasetSize());
        final Set<String> ids = list.stream().map(MergingResultDataModel::getOriginalId).collect(Collectors.toSet());
        log.info("未完成合并推送的数据集数量：{}", ids);
        if (CollUtil.isEmpty(ids)) {
            log.info("未找到未完成推送的数据集");
            return false;
        }
        // 与redis中缓存的key做比较，验证是否在处理中的数据id重复
        final Set<String> effectiveDataset = analysisService.effectiveDataset(ids);
        if (CollUtil.isEmpty(effectiveDataset)) {
            log.info("与redis标记数据对比后，无推送数据可执行 {}", ids);
            return false;
        }
        context.setIds(effectiveDataset);
        analysisService.markDataStatus(context.getIds(), -1);
        log.info("本次推送到数据清洗服务数据量：{}", context.getIds().size());
        return CollUtil.isNotEmpty(context.getIds());
    }

    @Override
    public boolean isAccess() {
        AnalysisDataContext context = this.getRequestData();
        return true;
    }
}

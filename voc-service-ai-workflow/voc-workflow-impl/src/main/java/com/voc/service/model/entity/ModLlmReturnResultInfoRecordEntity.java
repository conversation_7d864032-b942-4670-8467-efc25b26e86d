package com.voc.service.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Title: MergingResltData
 * @Package: com.voc.service.model.entity
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 16:38
 * @Version:1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "mod_llm_return_result_info_record")
public class ModLlmReturnResultInfoRecordEntity implements Serializable {

    private String newId; // 主键
    private String workId; // 接收处理标识
    private String originalId; // 原文ID
    private String clientId; // 客户标识
    private String vehicleBrand; // 品牌
    private String vehicleModel; // 车系
    private String scenario; // 场景
    private String subject; // 主体
    private String aspect; // 主体（注意：这里的注释可能与字段的实际用途不符，但保持原样）
    private String description; // 描述
    private String sentiment; // 情感
    private String intent; // 意图
    private String confidence; // 情感分析中的置信度（注意：原注释为“电池”，这里假设是注释错误）
    private LocalDateTime createTime; // 接收时间
    private String done;
    private String channelId;
    private String contentType;
    private Integer modelType;
    private Object extFields;
    @TableField(exist = false)
    private Boolean hightLevel;
    @TableField(exist = false)
    private Integer voiceCount;//声音数量
}

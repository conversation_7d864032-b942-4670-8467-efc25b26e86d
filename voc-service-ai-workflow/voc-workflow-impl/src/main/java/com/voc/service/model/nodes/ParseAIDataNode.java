package com.voc.service.model.nodes;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.voc.service.model.model.VoiceClipRequestDataModel;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.ModelContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.RetryException;
import org.springframework.util.Assert;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "parseAIDataNode", name = "解析并检查数据节点")
public class ParseAIDataNode extends AbstractNodeIf {

    private static final Logger log = LoggerFactory.getLogger(ParseAIDataNode.class);

    @Override
    public boolean processIf() throws Exception {
        try {
            ModelContext context = this.getRequestData();
            final VoiceClipRequestDataModel processingDataset = context.getInputDataset();
            if ("opinion".equalsIgnoreCase(context.getTag())) {
                log.debug("opinion 本次处理数据集:{}", processingDataset);
                //单个原文 -》 多个片段
                if (ObjectUtils.isEmpty(processingDataset)) {
                    log.warn("opinion 本次处理数据为空，不进行流程处理");
                    return false;
                }
            } else if ("scenario".equalsIgnoreCase(context.getTag())) {
                log.debug("scenario 本次处理数据集:{}", processingDataset);
                if (ObjectUtils.isEmpty(processingDataset)) {
                    log.warn("scenario 本次处理数据为空，不进行流程处理");
                    return false;
                }
            } else {
                throw new Exception("未找到匹配tag值");
            }

            context.setContentId(processingDataset.getContentId());
            return true;
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        ModelContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getTag()), "getTag cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(context.getClientId()), "getClientId cannot be empty");
        Assert.isTrue(ObjectUtil.isNotEmpty(context.getInputDataset()), "getInputDataset cannot be empty");
//        Assert.isTrue(CollUtil.isNotEmpty(context.getIds()), "getIds cannot be empty");


        return true;
    }
}

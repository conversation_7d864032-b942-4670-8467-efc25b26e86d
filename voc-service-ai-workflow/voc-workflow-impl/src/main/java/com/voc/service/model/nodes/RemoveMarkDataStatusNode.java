package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollUtil;
import com.voc.service.model.api.IAnalysisService;
import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.model.MergeStatusRecordModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.AnalysisDataContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "removeMarkDataStatusNode", name = "删除标记数据集状态（处理中）节点")
public class RemoveMarkDataStatusNode extends AbstractNode {
    private static final Logger log = LoggerFactory.getLogger(RemoveMarkDataStatusNode.class);
    @Autowired
    IAnalysisService analysisService;
    @Autowired
    IMergeStatusRecordService iMergeStatusRecordService;

    @Override
    public void process() throws Exception {
        try {
            AnalysisDataContext context = this.getRequestData();
            //删除redis中标价的状态数据
            Set<String> ids = context.getIds();
            log.info("删除标记数据集状态（处理中）节点，删除数据集ID：{}", ids);
            List<MergeStatusRecordModel> mergeStatusRecordList = iMergeStatusRecordService.getMergeStatusRecordList(ids);
            if (CollUtil.isNotEmpty(mergeStatusRecordList)) {
                Set<String> idList = mergeStatusRecordList.stream().map(MergeStatusRecordModel::getNewId).collect(Collectors.toSet());
                analysisService.removeMarkDataStatus(idList);
            }
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        AnalysisDataContext context = this.getRequestData();
        Assert.isTrue(CollUtil.isNotEmpty(context.getIds()), "getIds cannot be empty");


        return true;
    }

}

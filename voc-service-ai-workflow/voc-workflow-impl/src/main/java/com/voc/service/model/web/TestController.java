package com.voc.service.model.web;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.voc.service.common.response.Result;
import com.voc.service.common.util.ServiceContextHolder;
import com.voc.service.model.api.IAnalysisService;
import com.voc.service.model.runner.ListeningAiResultFlowRunner;
import com.voc.service.model.runner.ListeningUnpushedAiResultFlowRunner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.apache.kafka.clients.admin.NewTopic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.Arrays;
import java.util.Collection;
import java.util.Set;


@RestController
@RequestMapping("/")
public class TestController {
    private static final Logger log = LoggerFactory.getLogger(TestController.class);
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    ListeningAiResultFlowRunner listeningAiResultFlowRunner;
    @Autowired
    ListeningUnpushedAiResultFlowRunner listeningUnpushedAiResultFlowRunner;
    @Autowired
    KafkaTemplate<String, String> kafkaTemplate;
    @Autowired
    IAnalysisService analysisService;

    @Operation(summary = "测试")
    @Parameter(name = "Authorization", in = ParameterIn.HEADER, required = true, description = "Bearer [token]")
    @GetMapping("/testAiResultFlow")
    Result<?> testAiResultFlow() {
        try {
            listeningAiResultFlowRunner.run();
            return Result.OK(ServiceContextHolder.traceId());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("异常");
        }
    }

    @Operation(summary = "测试")
    @Parameter(name = "Authorization", in = ParameterIn.HEADER, required = true, description = "Bearer [token]")
    @GetMapping("/testinvokingRawNormalizerFlow")
    Result<?> testinvokingRawNormalizerFlow() {
        try {
            listeningUnpushedAiResultFlowRunner.run();
            return Result.OK(ServiceContextHolder.traceId());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("异常");
        }
    }


    @Operation(summary = "测试")
    @Parameter(name = "Authorization", in = ParameterIn.HEADER, required = true, description = "Bearer [token]")
    @GetMapping("/testKafka")
    Result<?> testKafka() {
        try {
            //resp-2022.5.zip
//            File file = FileUtil.file("D:\\360极速浏览器X下载\\b00305b74e140c9c0553807a1161f053.log");
            File file = FileUtil.file("D:\\360极速浏览器X下载\\nginx-1.24.0.zip");
            byte[] content = FileUtil.readBytes(file);
//            byte[] file = FileUtil.readBytes("D:\\360极速浏览器X下载\\resp-2022.5.zip");
            System.out.println(10000120);
            System.out.println(FileUtil.size(file));
            System.out.println(FileUtil.size(file) / (1024.0 * 1024.0));
//            new ProducerRecord("testSize", StrUtil.str(content, "UTF-8"));
            kafkaTemplate.send("testSize", StrUtil.str(content, "UTF-8"));
            return Result.OK();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("异常");
        }
    }

    @Operation(summary = "测试")
    @Parameter(name = "Authorization", in = ParameterIn.HEADER, required = true, description = "Bearer [token]")
    @GetMapping("/testRedis")
    Result<?> testRedis() {
        try {
            String key_status = "ai-workflow".concat("::status_analysis::");
            Set<String> keys = redisTemplate.keys(key_status.concat("*"));

            Collection<String> list = CollUtil.subtract(Arrays.asList("1", "2"), Arrays.asList("3", "2"));
            Collection<String> list1 = CollUtil.subtract(Arrays.asList("3", "2"), Arrays.asList("1", "2"));
            Collection<String> list2 = CollUtil.subtract(Arrays.asList("1", "2"), Arrays.asList());
            Collection<String> list3 = CollUtil.subtract(Arrays.asList(), Arrays.asList("1", "2"));


            log.info("", list);
            return Result.OK();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("异常");
        }
    }

    @Autowired
    KafkaAdmin kafkaAdmin;

    @Operation(summary = "测试")
    @Parameter(name = "Authorization", in = ParameterIn.HEADER, required = true, description = "Bearer [token]")
    @GetMapping("/test")
    Result<?> test() {
        try {
            NewTopic newTopic = new NewTopic("testC_1", 3, (short) 1);
            kafkaAdmin.createOrModifyTopics(newTopic);
            log.info("aaaaaaa");
            return Result.OK();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("异常");
        }
    }

}

package com.voc.service.model.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.model.api.IModMetaDataAnalysisService;
import com.voc.service.model.entity.ModLlmReturnResultInfoRecordEntity;
import com.voc.service.model.entity.ModMetaDataAnalysisEntity;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.ModMetaDataAnalysisMapper;
import com.voc.service.model.model.ModMetaDataAnalysisModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【mod_meta_data_analysis(大模型原文数据记录表)】的数据库操作Service实现
 * @createDate 2024-08-01 13:45:36
 */
@Service
public class ModMetaDataAnalysisServiceImpl extends ServiceImpl<ModMetaDataAnalysisMapper, ModMetaDataAnalysisEntity>
        implements IModMetaDataAnalysisService {

    @Autowired
    ModelConvertMapperService modelConvertMapperService;

    @Override
    public List<ModMetaDataAnalysisModel> findModMetaDataAnalysisList(List<String> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return null;
        }

        List<ModMetaDataAnalysisEntity> modMetaDataAnalysisList = new ArrayList<>();
        List<List<String>> subList = CollUtil.split(idList, 1000);
        subList.stream().forEach(sub -> modMetaDataAnalysisList.addAll(this.list(new QueryWrapper<ModMetaDataAnalysisEntity>().in("new_id", sub))));
        if (CollectionUtil.isEmpty(modMetaDataAnalysisList)) {
            return null;
        }
        return modelConvertMapperService.cenvertToModMetaDataAnalysisEntityList(modMetaDataAnalysisList);
    }

    @Override
    public boolean batchUpdateStatus(List<String> customIds, int status) {
        UpdateWrapper<ModMetaDataAnalysisEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(ModMetaDataAnalysisEntity::getNewId, customIds)
                .set(ModMetaDataAnalysisEntity::getDataStatus, 4)
                .set(ModMetaDataAnalysisEntity::getDone, 1);
        return this.update(updateWrapper);
    }

    @Override
    public long count(String id) {
        QueryWrapper<ModMetaDataAnalysisEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ModMetaDataAnalysisEntity::getNewId, id);
        return super.count(queryWrapper);
    }
}





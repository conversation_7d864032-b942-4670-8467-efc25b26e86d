package com.voc.service.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @创建者: fanrong
 * @创建时间: 2024/9/5 下午3:09
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "mod_opinion_and_tags_mapping")
public class OpinionAndTagsMappingEntity implements Serializable {
    /**
     * id
     */
     String id;
    /**
     * 评价主体
     */
     String subject;
    /**
     * 评价属性
     */
    String aspect;
    /**
     * 描述
     */
    String description;
    /**
     * 观点
     */
    String opinion;
    /**
     * 归一观点
     */
    String topic;
    /**
     * 业务标签
     */
    String businessTag;
    /**
     * 业务标签
     */
    String qualityTag;
    /**
     * 场景标签
     */
    String scenarioTag;
    /**
     * 情感
     */
    String sentiment;
    /**
     * 意图
     */
    String intention;

}

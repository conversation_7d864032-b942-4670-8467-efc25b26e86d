package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollectionUtil;
import com.voc.service.common.util.IdWorker;
import com.voc.service.model.api.IModLlmReturnResultInfoRecordService;
import com.voc.service.model.api.IModMetaDataAnalysisService;
import com.voc.service.model.enums.ModelTypeEnum;
import com.voc.service.model.model.MergeStatusRecordModel;
import com.voc.service.model.model.ModLlmReturnResultInfoRecordModel;
import com.voc.service.model.model.ModMetaDataAnalysisModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.voc.service.trhird.model.ZhiPuAiContentModel;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import jodd.util.StringUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Title: saveAIModelResultAnalysisNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "saveAIModelResultAnalysisNode", name = "保存第三方服务返回值解析后的数据节点")
public class SaveAIModelResultAnalysisNode extends AbstractNode {

    private static final Logger log = LoggerFactory.getLogger(SaveAIModelResultAnalysisNode.class);
    @Autowired
    IModLlmReturnResultInfoRecordService iModLlmReturnResultInfoRecordService;

    @Autowired
    IModMetaDataAnalysisService iModMetaDataAnalysisService;

    @Override
    public void process() throws Exception {
        try {
            ThirdPartyAIContext context = this.getRequestData();
            List<ZhiPuAiContentModel> zhiPuAiContentModelList = context.getZhiPuAiContentModelList();
            if (CollectionUtil.isEmpty(zhiPuAiContentModelList)) {
                log.info("zhiPuAiContentModelList is empty");
                return;
            }
            List<ModLlmReturnResultInfoRecordModel> modelList = new CopyOnWriteArrayList<>();
               List<MergeStatusRecordModel> mergeStatusRecordModelList = new ArrayList<>();
            List<String> customIdList = zhiPuAiContentModelList.stream().map(ZhiPuAiContentModel::getCustomId).toList();
            List<ModMetaDataAnalysisModel> modMetaDataAnalysisList = iModMetaDataAnalysisService.findModMetaDataAnalysisList(customIdList);
            if (CollectionUtil.isEmpty(modMetaDataAnalysisList)) {
                log.info("modMetaDataAnalysisList is empty");
                return;
            }
            Map<String, ModMetaDataAnalysisModel> metaDataAnalysisModelMap = modMetaDataAnalysisList.stream().collect(Collectors.toMap(ModMetaDataAnalysisModel::getNewId, Function.identity()));
            for (ZhiPuAiContentModel contentModel : zhiPuAiContentModelList) {
                if (StringUtil.isBlank(contentModel.getCustomId())) {
                    log.info("customId is empty");
                    continue;
                }
                List<ZhiPuAiContentModel.Vehicle> vehicleList = contentModel.getVehicleList();
                if (MapUtils.isNotEmpty(metaDataAnalysisModelMap) && metaDataAnalysisModelMap.containsKey(contentModel.getCustomId())) {
                    ModMetaDataAnalysisModel modMetaDataAnalysisModel = metaDataAnalysisModelMap.get(contentModel.getCustomId());
                    context.setWorkId(modMetaDataAnalysisModel.getWorkId());
                    context.setClientId(modMetaDataAnalysisModel.getClientId());
                    context.setContentType(modMetaDataAnalysisModel.getContentType());
                    context.setChannelId(modMetaDataAnalysisModel.getChannelId());
                    context.setModelType(modMetaDataAnalysisModel.getModelType());
                } else {
                    log.info("modMetaDataAnalysisModel is empty");
                    continue;
                }
                if (CollectionUtil.isEmpty(vehicleList)) {
                    ModLlmReturnResultInfoRecordModel model = new ModLlmReturnResultInfoRecordModel();
                    model.setNewId(IdWorker.getId());
                    this.makeMergingResultStatusData(contentModel.getCustomId(), model.getNewId(), context, null, null, mergeStatusRecordModelList);
                    model.setWorkId(context.getWorkId());
                    model.setClientId(context.getClientId());
                    model.setModelType(context.getModelType());
                    model.setContentType(context.getContentType());
                    model.setChannelId(context.getChannelId());
                    model.setOriginalId(contentModel.getCustomId());
                    model.setVehicleBrand("");
                    model.setVehicleModel("");
                    model.setCreateTime(LocalDateTime.now());
                    model.setHightLevel(ObjectUtils.isNotEmpty(context.getModelType()) && Objects.equals(context.getModelType(), ModelTypeEnum.AI_ONLINE.getType()));
                    model.setDone("0");
                    modelList.add(model);
                    continue;
                }
                for (ZhiPuAiContentModel.Vehicle vehicle : vehicleList) {
                    List<ZhiPuAiContentModel.Viewpoint> viewpoints = vehicle.getViewpoints();
                    if (CollectionUtil.isEmpty(viewpoints)) {
                        ModLlmReturnResultInfoRecordModel model = new ModLlmReturnResultInfoRecordModel();
                        model.setNewId(IdWorker.getId());
                        this.makeMergingResultStatusData(contentModel.getCustomId(), model.getNewId(), context, vehicle, null, mergeStatusRecordModelList);
                        model.setWorkId(context.getWorkId());
                        model.setClientId(context.getClientId());
                        model.setModelType(context.getModelType());
                        model.setContentType(context.getContentType());
                        model.setChannelId(context.getChannelId());
                        model.setOriginalId(contentModel.getCustomId());
                        model.setVehicleBrand(vehicle.getVehicleBrand());
                        model.setVehicleModel(vehicle.getVehicleModel());
                        model.setCreateTime(LocalDateTime.now());
                        model.setHightLevel(ObjectUtils.isNotEmpty(context.getModelType()) && Objects.equals(context.getModelType(), ModelTypeEnum.AI_ONLINE.getType()));
                        model.setDone("0");
                        modelList.add(model);
                        continue;
                    }
                    for (ZhiPuAiContentModel.Viewpoint viewpoint : viewpoints) {
                        ModLlmReturnResultInfoRecordModel model = new ModLlmReturnResultInfoRecordModel();
                        model.setNewId(IdWorker.getId());
                        this.makeMergingResultStatusData(contentModel.getCustomId(), model.getNewId(), context, vehicle, viewpoint, mergeStatusRecordModelList);
                        model.setWorkId(context.getWorkId());
                        model.setClientId(context.getClientId());
                        model.setModelType(context.getModelType());
                        model.setContentType(context.getContentType());
                        model.setChannelId(context.getChannelId());
                        model.setOriginalId(contentModel.getCustomId());
                        model.setVehicleBrand(vehicle.getVehicleBrand());
                        model.setVehicleModel(vehicle.getVehicleModel());
                        model.setAspect(viewpoint.getAspect());
                        model.setSubject(viewpoint.getSubject());
                        model.setDescription(viewpoint.getDesc());
                        model.setConfidence(viewpoint.getConfidence());
                        model.setIntent(viewpoint.getIntent());
                        model.setScenario(viewpoint.getScenario());
                        model.setSentiment(viewpoint.getSentiment());
                        model.setHightLevel(ObjectUtils.isNotEmpty(context.getModelType()) && Objects.equals(context.getModelType(), ModelTypeEnum.AI_ONLINE.getType()));
                        model.setCreateTime(LocalDateTime.now());
                        model.setDone("0");
                        modelList.add(model);
                    }
                }
            }
            int resultData = iModLlmReturnResultInfoRecordService.saveModLlmResultData(context.getClientId(), modelList);
            log.info("智谱返回结果入库数量:{}", resultData);
            context.setMergeStatusRecordModelList(mergeStatusRecordModelList);
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }


    /**
     * 组装批量插入合并状态数据
     *
     * @param customId
     * @param voiceId
     * @param vehicle
     * @param viewpoint
     * @return
     */
    private void makeMergingResultStatusData(String customId, String voiceId, ThirdPartyAIContext context,
                                             ZhiPuAiContentModel.Vehicle vehicle,
                                             ZhiPuAiContentModel.Viewpoint viewpoint,
                                             List<MergeStatusRecordModel> mergeStatusRecordModelList) {
        MergeStatusRecordModel model = new MergeStatusRecordModel();
        model.setNewId(voiceId);
        model.setClientId(context.getClientId());
        model.setWorkId(context.getWorkId());
        model.setModelType(context.getModelType());
        model.setContentType(context.getContentType());
        model.setChannelId(context.getChannelId());
        model.setOriginalId(customId);
        model.setVoiceId(voiceId);
        model.setBrandCarNum(-1);
        model.setLabelNum(-1);
        model.setScenarioNum(-1);
        model.setDone(0);
        model.setPresetStatus(14);
        model.setCreateTime(LocalDateTime.now());
        mergeStatusRecordModelList.add(model);
    }

}

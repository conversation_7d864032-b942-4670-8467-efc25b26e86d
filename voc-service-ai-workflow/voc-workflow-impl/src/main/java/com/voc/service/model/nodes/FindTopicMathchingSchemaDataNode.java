package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.voc.service.model.api.ICarAndDimOpinionMappingService;
import com.voc.service.model.api.IScenariosTagsLevelMappingService;
import com.voc.service.model.model.CarAndDimOpinionMappingModel;
import com.voc.service.model.model.ScenariosTagsLevelMappingModel;
import com.voc.service.model.model.VoiceClipRequestDataModel;
import com.voc.service.model.model.VoiceClipResultData;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ModelContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryException;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "findTopicMatchingSchemaDataNode", name = "查询Tag对应的schema数据节点")
public class FindTopicMathchingSchemaDataNode extends AbstractNode {
    private static final Logger log = LoggerFactory.getLogger(FindTopicMathchingSchemaDataNode.class);
    @Autowired
    ICarAndDimOpinionMappingService carAndDimOpinionMappingService;
    @Autowired
    IScenariosTagsLevelMappingService scenariosTagsLevelMappingService;

    @Override
    public void process() throws Exception {
        try {
            ModelContext context = this.getRequestData();
            final List<VoiceClipResultData> processingLabeledDataset = context.getProcessingLabeledDataset();
            final VoiceClipRequestDataModel inputDataset = context.getInputDataset();
            if ("opinion".equalsIgnoreCase(context.getTag())) {
                //获取整车体系+评价维度关系映射
                Map<String, List<CarAndDimOpinionMappingModel>> allCarAndDimOpinionMapping = carAndDimOpinionMappingService.findAllCarAndDimOpinionMapping();
                if (ObjectUtils.isEmpty(allCarAndDimOpinionMapping)) {
//                    throw new Exception("opinion 映射表无数据");
                    log.error("opinion 映射表无数据");
                    context.setProcessingUnLabeledDataset(processingLabeledDataset);
                    context.setProcessingLabeledDataset(Collections.EMPTY_LIST);
                    context.setNodeIdentifier("findTopicMatchingSchemaDataNode");
                    return;
                }
                List<VoiceClipResultData> voiceClipResultDataList = new CopyOnWriteArrayList<>();
                List<VoiceClipResultData> processingUnLabeledData = new CopyOnWriteArrayList<>();
                //遍历已命中的数据集，匹配出可用整车体系+评价维度，若匹配到，则设置car_schema和evaluate_schema并结束循环，否则，则将数据放入未命中数据集
                for (VoiceClipResultData voiceClipResultData : processingLabeledDataset) {
                    //获取opinion
                    final String simOpinion = voiceClipResultData.getSim_opinion();
                    //将sim_opinion转md5
//                    final String md5Hex = DigestUtil.md5Hex(simOpinion);
                    final String md5Hex = carAndDimOpinionMappingService.getMd5(simOpinion);
                    //判断整车体系+评价维度关系映射表是否包含opinion
                    if (allCarAndDimOpinionMapping.containsKey(md5Hex)) {
                        List<CarAndDimOpinionMappingModel> carAndDimOpinionMappingModels = allCarAndDimOpinionMapping.get(md5Hex);
//                        List<CarAndDimOpinionMappingModel> collect = carAndDimOpinionMappingModels.stream().filter(e -> ObjectUtils.isNotEmpty(e.getTopic())).collect(Collectors.toList());
                        List<CarAndDimOpinionMappingModel> collect = carAndDimOpinionMappingModels.stream().filter(e -> ObjectUtils.isNotEmpty(e.getDim())).collect(Collectors.toList());
                        if (ObjectUtils.isNotEmpty(collect)) {
                            final CarAndDimOpinionMappingModel carAndDimOpinionMappingModel = collect.get(0);
                            //评价维度
//                            voiceClipResultData.setEvaluate_schema(carAndDimOpinionMappingModel.getTopic());
                            voiceClipResultData.setEvaluate_schema(carAndDimOpinionMappingModel.getDim());
                            //整车体系
                            voiceClipResultData.setCar_schema(ObjectUtils.isEmpty(carAndDimOpinionMappingModel.getCar()) ? "" : carAndDimOpinionMappingModel.getCar());
                            voiceClipResultDataList.add(voiceClipResultData);
                            log.info("匹配到可用整车体系+评价维度的opinion数据,执行下一个节点");
                            break;
                        }
                    } else {
                        processingUnLabeledData.add(voiceClipResultData);
                    }
                }
                if (ObjectUtils.isNotEmpty(voiceClipResultDataList)) {
                    //整车体系+评价维度关系映射表 中存在匹配数据，将数据放入已命中数据集
                    context.setProcessingLabeledDataset(voiceClipResultDataList);
                } else {
                    //整车体系+评价维度关系映射表 中无匹配数据，将数据放入未命中数据集并将已命中数据集置空
                    context.setProcessingLabeledDataset(voiceClipResultDataList);
                    context.setProcessingUnLabeledDataset(Arrays.asList(VoiceClipResultData.builder().id(inputDataset.getNew_id()).build()));
                    context.setNodeIdentifier("findTopicMatchingSchemaDataNode");
                }
            } else if ("scenario".equalsIgnoreCase(context.getTag())) {
                //获取用车场景关系映射
                Map<String, List<ScenariosTagsLevelMappingModel>> allScenariosTagsLevelMapping = scenariosTagsLevelMappingService.findAllScenariosTagsLevelMapping();
                if (ObjectUtils.isEmpty(allScenariosTagsLevelMapping)) {
                    log.warn("opinion 映射表无数据");
                    context.setProcessingUnLabeledDataset(Arrays.asList(VoiceClipResultData.builder().id(inputDataset.getNew_id()).build()));
                    context.setProcessingLabeledDataset(Collections.EMPTY_LIST);
                    context.setNodeIdentifier("findTopicMatchingSchemaDataNode");
                    return;
                }
                List<VoiceClipResultData> voiceClipResultDataList = new CopyOnWriteArrayList<>();
                // 遍历已命中的数据集，匹配出可用用车场景，若匹配到，则设置scenario_schema并结束循环，否则，则将数据放入未命中数据集
                for (VoiceClipResultData voiceClipResultData : processingLabeledDataset) {
                    //获取tagsLevel4
                    final String tagsLevel4 = voiceClipResultData.getTagsLevel4();
                    //将tagsLevel4转md5
//                    final String md5Hex = DigestUtil.md5Hex(tagsLevel4);
                    final String md5Hex = carAndDimOpinionMappingService.getMd5(tagsLevel4);
                    //判断用车场景关系映射表是否包含tagsLevel4
                    if (allScenariosTagsLevelMapping.containsKey(md5Hex)) {
                        List<ScenariosTagsLevelMappingModel> scenariosTagsLevelMappingModels = allScenariosTagsLevelMapping.get(md5Hex);
                        voiceClipResultData.setScenariosTagsLevelMappingList(scenariosTagsLevelMappingModels);
                        voiceClipResultDataList.add(voiceClipResultData);
                        log.info("匹配到可用的用车场景的scenario数据,执行下一个节点");
                        break;
                    } else {
                        voiceClipResultDataList.add(voiceClipResultData);
                    }
                }
                if (ObjectUtils.isNotEmpty(voiceClipResultDataList)) {
                    //用车场景关系映射表 中存在匹配数据，将数据放入已命中数据集
                    context.setProcessingLabeledDataset(voiceClipResultDataList);
                } else {
                    //用车场景关系映射表 中无匹配数据，将数据放入未命中数据集并将已命中数据集置空
                    context.setProcessingLabeledDataset(voiceClipResultDataList);
                    context.setProcessingUnLabeledDataset(Arrays.asList(VoiceClipResultData.builder().id(inputDataset.getNew_id()).build()));
                    context.setNodeIdentifier("findTopicMatchingSchemaDataNode");
                }
            } else {
                throw new Exception("未找到匹配tag值");
            }
            return;
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        ModelContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getTag()), "getTag cannot be empty");
        Assert.isTrue(CollUtil.isNotEmpty(context.getProcessingLabeledDataset()), "getProcessingLabeledDataset cannot be empty");


        return true;
    }

}

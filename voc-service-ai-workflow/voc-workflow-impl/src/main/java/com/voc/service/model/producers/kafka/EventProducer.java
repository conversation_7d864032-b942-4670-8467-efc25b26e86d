package com.voc.service.model.producers.kafka;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.voc.service.analysis.dto.MessageDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CatListener
 * @createTime 2024年03月14日 9:46
 * @Copyright cuick
 */
@Component("enventCusumer.producer.kafka")
public class EventProducer {
    public static final String TOPIC_EVENT = "VDP_aiFlowEvent";
    private static final Logger log = LoggerFactory.getLogger(EventProducer.class);
    @Autowired
    KafkaTemplate<String, String> kafkaTemplate;

    public void pushEvent(MessageDTO msg) throws Exception {
        Assert.isTrue(StrUtil.isNotBlank(msg.getType()), "getType cannot be empty");
        log.info("推送事件数据 {}", msg.getType());
        String sendText = JSONUtil.toJsonStr(msg, JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
        kafkaTemplate.send(TOPIC_EVENT, sendText);
    }
}


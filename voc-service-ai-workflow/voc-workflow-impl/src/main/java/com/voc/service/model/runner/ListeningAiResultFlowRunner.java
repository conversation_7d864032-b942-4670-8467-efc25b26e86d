package com.voc.service.model.runner;

import cn.hutool.core.collection.CollectionUtil;
import com.voc.service.model.api.IModLlmBatchInfoRecordService;
import com.voc.service.model.enums.BatchStatusEnum;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.voc.service.model.vo.ModLlmBatchInfoRecordVo;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * @Title: ListeningAiResultFlowRunner
 * @Package: com.voc.service.analysis.core.v2.runner
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 16:14
 * @Version:1.0
 */
@Component
public class ListeningAiResultFlowRunner /*implements CommandLineRunner*/ {
    private static final Logger log = LoggerFactory.getLogger(ListeningAiResultFlowRunner.class);
    @Resource
    private FlowExecutor flowExecutor;
    @Autowired
    Executor executor;
    final String chainId = "listening_ai_result_flow";

    @Autowired
    IModLlmBatchInfoRecordService modLlmBatchInfoRecordService;

    @Resource
    RedisTemplate redisTemplate;


    public ListeningAiResultFlowRunner() {
        log.info("--->> {}", this.getClass().getSimpleName());
    }

    @Scheduled(cron = "0/30 * * * * ?")
    @XxlJob("listeningAiResultFlowRunner")
    public void run() {
        ThirdPartyAIContext context = ThirdPartyAIContext.builder().build();
        List<ModLlmBatchInfoRecordVo> batchIdList = null;
        try {
            List<String> statusList = Arrays.asList(
                    BatchStatusEnum.COMPLETED.getCode(),
                    BatchStatusEnum.EXPIRED.getCode(),
                    BatchStatusEnum.CANCELLING.getCode(),
                    BatchStatusEnum.CANCELLED.getCode());
            batchIdList = modLlmBatchInfoRecordService.findBatchIdList(statusList);
            log.info("查询批次待处理数据:{}", batchIdList);
            if (CollectionUtil.isEmpty(batchIdList)) {
                return;
            }
            for (ModLlmBatchInfoRecordVo modLlmBatchInfoRecordVo : batchIdList) {
                String batchId = null;
                try {
                    if (StringUtils.isEmpty(modLlmBatchInfoRecordVo.getBatchId())) {
                        continue;
                    }
                    batchId = modLlmBatchInfoRecordVo.getBatchId();
                    Boolean flag = redisTemplate.opsForValue().setIfAbsent(batchId, "1", 1, TimeUnit.HOURS);
                    // 加锁失败，已有消费端在此时对此消息进行处理，这里不再做处理
                    if (Boolean.FALSE.equals(flag)) {
                        log.info("batchId {} 正在处理中 ", batchId);
                        continue;
                    }
                    context.setBatchId(batchId);
                    context.getStopWatch().start("执行 ".concat(chainId));
                    LiteflowResponse response = flowExecutor.execute2Resp(chainId, context,context.getWorkId());
                    if (!response.isSuccess()) {
                        log.error("workId:{}  {}", context.getWorkId(), response.getCause());
                        throw response.getCause();
                    }
                    redisTemplate.delete(batchId);
                } catch (Exception e) {
                    if (StringUtils.isNotBlank(batchId)) {
                        redisTemplate.delete(batchId);
                    }
                    log.error("listeningAiResultFlowRunner:", e.getMessage());
                }
            }
        } catch (Exception e) {
            if (CollectionUtil.isNotEmpty(batchIdList)) {
                for (ModLlmBatchInfoRecordVo modLlmBatchInfoRecordVo : batchIdList) {
                    if (StringUtils.isEmpty(modLlmBatchInfoRecordVo.getBatchId())) {
                        continue;
                    }
                    redisTemplate.delete(modLlmBatchInfoRecordVo.getBatchId());
                }
            }
            log.error("离线解析数据处理异常:", e);
        }
    }
}

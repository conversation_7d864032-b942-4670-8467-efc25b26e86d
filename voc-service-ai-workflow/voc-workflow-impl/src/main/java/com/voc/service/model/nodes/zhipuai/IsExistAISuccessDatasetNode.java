package com.voc.service.model.nodes.zhipuai;


import cn.hutool.core.collection.CollUtil;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.retry.RetryException;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "isExistAISuccessDatasetNode", name = "检查是否有成功数据结果集节点")
public class IsExistAISuccessDatasetNode extends AbstractNodeIf {

    @Override
    public boolean processIf() throws Exception {
        try {
            ThirdPartyAIContext context = this.getRequestData();
            return CollUtil.isNotEmpty(context.getZhiPuAiContentModelList());
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }

}

package com.voc.service.model.producers.kafka;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.model.model.ModLlmBatchInfoRecordDetailedModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CatListener
 * @createTime 2024年03月14日 9:46
 * @Copyright cuick
 * @Description 推送到数据清洗服务
 */
@Component("analysis.producer.kafka")
public class AnalysisProducer {
    public static final String TOPIC_DATA = "VDP_fromModel";
    public static final String TO_TOPIC_DATA = "VDP_toModel";

    public static final String TOPIC_LLM_BATCH_CONTENT_ID = "VDP_modLLmBatchContentId";
    private static final Logger log = LoggerFactory.getLogger(AnalysisProducer.class);
    @Autowired
    KafkaTemplate<String, String> kafkaTemplate;

    /**
     * 推送数据至数据清洗服务监听的MQ队列
     *
     * @param msg
     * @throws Exception
     */
    public void pushData(MessageDTO msg) throws Exception {
        log.info("推送数据 {}", msg.getType());
        String sendText = JSONUtil.toJsonStr(msg.getData(), JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
        kafkaTemplate.send(TOPIC_DATA, sendText);
    }
    public void pushDataToModel(MessageDTO msg) throws Exception {
        Assert.isTrue(StrUtil.isNotBlank(msg.getSource()), "getSource cannot be empty");
        Assert.isTrue(ObjUtil.isNotNull(msg.getData()), "getData cannot be empty");

        final String sendText = JSONUtil.toJsonStr(msg.getData(), JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
        kafkaTemplate.send(TO_TOPIC_DATA,sendText);
    }

    public void pushBatchContentIds(List<ModLlmBatchInfoRecordDetailedModel> batchInfoRecordDetailed) {
        long startTime = System.currentTimeMillis();  // 记录开始时间
        for (ModLlmBatchInfoRecordDetailedModel modLlmBatchInfoRecordDetailedModel : batchInfoRecordDetailed) {
            try {
                kafkaTemplate.send(TOPIC_LLM_BATCH_CONTENT_ID.concat("_").concat("0"), JSONUtil.toJsonStr(modLlmBatchInfoRecordDetailedModel));
            } catch (Exception e) {
                log.error("topic:{} 推送数据失败 {}", TOPIC_LLM_BATCH_CONTENT_ID, JSONUtil.toJsonStr(modLlmBatchInfoRecordDetailedModel));
                e.printStackTrace();
            }
        }
        long endTime = System.currentTimeMillis();  // 记录结束时间
        double durationInSeconds = (endTime - startTime) / 1000.0;  // 将毫秒转换为秒

        log.info("topic modLLmBatchContentId 推送数据到Kafka耗时: {} 秒", durationInSeconds);
    }
}


package com.voc.service.model.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.model.api.IModCarNotSceneResultService;
import com.voc.service.model.entity.ModCarNotSceneResultEntity;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.ModCarNotSceneResultMapper;
import com.voc.service.model.model.ModCarNotSceneResultModel;
import com.voc.service.model.producers.kafka.ModelResultProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Title: ModCarSceneResultServiceImpl
 * @Package: com.voc.service.model.impl
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/6 10:23
 * @Version:1.0
 */
@Service
public class ModCarNotSceneResultServiceImpl extends ServiceImpl<ModCarNotSceneResultMapper, ModCarNotSceneResultEntity>
        implements IModCarNotSceneResultService {
    @Autowired
    ModelConvertMapperService convertMapperService;

    @Autowired
    ModelResultProducer modelResultProducer;

    @Override
    public boolean add(String clientId, List<ModCarNotSceneResultModel> list) {
        List<ModCarNotSceneResultEntity> modCarNotSceneResultEntities = convertMapperService.cenvertToModCarNotSceneResultEntityList(list);
        modelResultProducer.pushNotCarSceneResultData(MessageDTO.builder().source(clientId).data(modCarNotSceneResultEntities).build());
        return Boolean.TRUE;
    }

}

package com.voc.service.model.nodes;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.voc.service.model.config.AIWorkflowConfig;
import com.voc.service.model.model.VoiceClipResultData;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.ModelContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "greaterThanScoreThresholdNode", name = "数值大于设定阈值节点")
public class GreaterThanScoreThresholdNode extends AbstractNodeIf {
    private static final Logger log = LoggerFactory.getLogger(GreaterThanScoreThresholdNode.class);
    @Autowired
    AIWorkflowConfig config;

    @Override
    public boolean processIf() throws Exception {
        ModelContext context = this.getRequestData();
        final float threshold;
        final List<VoiceClipResultData> processingLabeledDataset = context.getProcessingLabeledDataset();
        if ("opinion".equalsIgnoreCase(context.getTag())) {
            threshold = config.getOpinionScoreThreshold();
//            threshold = 0.8F;
            // 过滤出大于设定阈值的数据
            List<VoiceClipResultData> voiceClipResultDataList = processingLabeledDataset.stream().filter(e -> {
                final Float score = e.getScore();
                Assert.isTrue(ObjectUtil.isNotNull(score), "score cannot be empty");
                return isGreater(score, threshold);
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(voiceClipResultDataList)) {
                log.info("存在数值大于设定[阈值:{}]的opinion数据:{}条，执行下一个节点", threshold, voiceClipResultDataList.size());
                context.setProcessingLabeledDataset(voiceClipResultDataList);
                return true;
            } else {
                log.info("不存在数值大于设定[阈值:{}]的opinion数据，执行下一个节点", threshold);
                context.setProcessingUnLabeledDataset(processingLabeledDataset);
                context.setProcessingLabeledDataset(Collections.EMPTY_LIST);
                return false;
            }
        } else if ("scenario".equalsIgnoreCase(context.getTag())) {
            threshold = config.getScenarioScoreThreshold();
//            threshold = 0.5F;
            // 过滤出大于设定阈值的数据
            List<VoiceClipResultData> voiceClipResultDataList = processingLabeledDataset.stream().filter(e -> {
                final Float score = e.getScore();
                Assert.isTrue(ObjectUtil.isNotNull(score), "score cannot be empty");
                return isGreater(score, threshold);
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(voiceClipResultDataList)) {
                log.info("存在数值大于设定[阈值:{}]的scenario数据:{}条，执行下一个节点", threshold, voiceClipResultDataList.size());
                context.setProcessingLabeledDataset(voiceClipResultDataList);
                return true;
            } else {
                log.info("不存在数值大于设定[阈值:{}]的scenario数据，执行下一个节点", threshold);
                context.setProcessingUnLabeledDataset(processingLabeledDataset);
                context.setProcessingLabeledDataset(Collections.EMPTY_LIST);
                return false;
            }
        } else {
            threshold = -1;
        }
        return false;

    }

    private boolean isGreater(float source, float target) {
        final BigDecimal source_ = new BigDecimal(Float.toString(source));
        source_.setScale(2, BigDecimal.ROUND_HALF_UP);

        final BigDecimal target_ = new BigDecimal(Float.toString(target));
        target_.setScale(2, BigDecimal.ROUND_HALF_UP);

        return NumberUtil.isGreater(source_, target_);
    }

    @Override
    public boolean isAccess() {
        ModelContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getTag()), "getTag cannot be empty");
        Assert.isTrue(CollUtil.isNotEmpty(context.getProcessingLabeledDataset()), "getTag cannot be empty");


        return true;
    }

}

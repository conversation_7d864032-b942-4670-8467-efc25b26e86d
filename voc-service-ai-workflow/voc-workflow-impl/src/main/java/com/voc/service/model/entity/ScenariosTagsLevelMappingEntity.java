package com.voc.service.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @创建者: fanrong
 * @创建时间: 2024/8/6 下午4:39
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "mod_scenarios_tags_level")
public class ScenariosTagsLevelMappingEntity implements Serializable {
    @TableField("level_1")
    private String level1;
    @TableField("level_2")
    private String level2;
    @TableField("level_3")
    private String level3;
    @TableField("level_4")
    private String level4;
}

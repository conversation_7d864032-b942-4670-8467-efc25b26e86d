<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <!-- 接收数据-保存原始数据 -->
    <chain name="meta_data_process_flow">
        THEN(
            IF(validateMetaDataNode, THEN(
                saveMeateDataNode,
                notificationNode.tag("invokingAIModel")
            ))
        );
    </chain>

    <!-- 调用第三方模型服务 -->
    <chain name="invoking_ai_model_flow">
        THEN(
            IF(modelOffTypeNode, THEN(
               aiOffModelZhiPuAiNode
            )),
            IF(modelOnlineTypeNode, THEN(
               aiOnlineModelTypeNode,
               IF(isExistAISuccessDatasetNode, THEN(
                   saveAIModelResultAnalysisNode,
                   saveMergeResultStatusAnalysisNode
               ))
            ))
        );
    </chain>


    <!-- 监听第三方服务返回结果，根据数据集调用voc模型结果 -->
    <chain name="listening_ai_result_flow">
        IF(loadAIModelBatchNumberNode,THEN(
           invokingAIModelStatusResultNode,
           IF(loadAIModelBatchStatusNode,THEN(
                invokingAIModelDownloadDataNode,
                IF(isExistAISuccessDatasetNode, THEN(
                   saveAIModelResultAnalysisNode,
                   modifyAIModelBatchNumberNode,
                   saveMergeResultStatusAnalysisNode
                ))
            )),
            IF(isExistAIFailureDatasetNode, THEN(
                disposeAIFailureDatasetNode
            ))
        ));
    </chain>

    <chain name="invoking_raw_normalizer_flow">
        IF(isExistAIResultUnprocessedDataSetNode,THEN(
            loadAIResultUnprocessedDataNode,
            addRedisStatusDataNode,
            IF(filteredRequiredFieldsDatasetDataNode,THEN(
                saveUnprocessedDatasetDataNode,
                filterModifyResultDataStatusNode
            )),
            WHEN(
                THEN(
                    invokingRawBrandNode.tag("brand"),
                    saveBrandTagMappingResultDataNode,
                    recordMatchingSchemaDataSizeV2Node,
                    modifyResultDataStatusNode
                ),
                THEN(
                    invokingRawNormalizerNode.tag("opinion"),
                    modifyResultDataStatusNode
                ),
                THEN(
                    invokingRawNormalizerNode.tag("scenario"),
                    modifyResultDataStatusNode
                )
            )
        ));
    </chain>
	
	<chain name="invoking_raw_normalizer_flow_v2">
        IF(isExistAIResultUnprocessedDataSetV2Node,THEN(
            loadAIResultUnprocessedDataNode,
            addRedisStatusDataNode,
            IF(filteredRequiredFieldsDatasetDataNode,THEN(
                saveUnprocessedDatasetDataNode,
                filterModifyResultDataStatusNode
            )),
              IF(isExistBrandNodeNode,THEN(
                invokingRawBrandNode.tag("brand"),
                saveBrandTagMappingResultDataNode,
                recordMatchingSchemaDataSizeV2Node,
                modifyResultDataStatusNode.tag("brand")
             )),
              IF(isExistOpinionNodeNode,THEN(
                invokingRawNormalizerNode.tag("opinion"),
                modifyResultDataStatusNode.tag("opinion")
              )),
              IF(isExistScenarioNodeNode,THEN(
                invokingRawNormalizerNode.tag("scenario"),
                modifyResultDataStatusNode.tag("scenario")
              ))
        ));
    </chain>
	
	<chain name="raw_normalizer_flow_v2">
        IF(parseAIDataNode, THEN(
            computeMatchingScoreNode,
            findTopicDataNode,
            IF(hasMatchingTagDataNode, THEN(
                IF(greaterThanScoreThresholdNode, THEN(
                    emptyNode
                )).ELIF(lessThanOrEqualScoreThresholdNode, THEN(
                    IF(isNotExistMatchingSchemaDataNode, WHEN(
                        recordParseSchemaDataDataSizeNode.tag("one"),
                        saveUnmatchedDataToDBNode
                    ))
                )),
                IF(isMatchingModelTagNode.tag("opinion"), THEN(
                    IF(loadGWMTagMappingDataV2Node,WHEN(
                        recordParseSchemaDataDataSizeNode,
                        saveTagMappingResultDataNode
                    )).ELSE(WHEN(
                        recordParseSchemaDataDataSizeNode.tag("one"),
                        saveUnmatchedDataToDBNode
                    ))
                )).ELIF(isMatchingModelTagNode.tag("scenario"), THEN(
                    IF(loadScenarioTagMappingDataNode,WHEN(
                        recordParseSchemaDataDataSizeNode,
                        saveTagMappingResultDataNode
                    )).ELSE(WHEN(
                        recordParseSchemaDataDataSizeNode.tag("one"),
                        saveUnmatchedDataToDBNode
                    ))
                )).ELSE(WHEN(
                    recordParseSchemaDataDataSizeNode.tag("one"),
                    saveUnmatchedDataToDBNode
                ))
            )).ELSE(WHEN(
                recordParseSchemaDataDataSizeNode.tag("one"),
                saveUnmatchedDataToDBNode
            ))
        ));
    </chain>
	

    <chain name="raw_normalizer_flow">
        IF(parseAIDataNode, THEN(
            computeMatchingScoreNode,
            findTopicDataNode,
            IF(hasMatchingTagDataNode, THEN(
                IF(greaterThanScoreThresholdNode, THEN(
                    findTopicMatchingSchemaDataNode,
                    IF(isNotExistMatchingSchemaDataNode, WHEN(
                        recordParseSchemaDataDataSizeNode.tag("one"),
                        saveUnmatchedDataToDBNode
                    ))
                )).ELIF(lessThanOrEqualScoreThresholdNode, THEN(
                    IF(isNotExistMatchingSchemaDataNode, WHEN(
                        recordParseSchemaDataDataSizeNode.tag("one"),
                        saveUnmatchedDataToDBNode
                    ))
                )),
                IF(isMatchingModelTagNode.tag("opinion"), THEN(
                    IF(loadGWMTagMappingDataNode,WHEN(
                        recordParseSchemaDataDataSizeNode,
                        saveTagMappingResultDataNode
                    )).ELSE(WHEN(
                        recordParseSchemaDataDataSizeNode.tag("one"),
                        saveUnmatchedDataToDBNode
                    ))
                )).ELIF(isMatchingModelTagNode.tag("scenario"), THEN(
                    IF(loadScenarioTagMappingDataNode,WHEN(
                        recordParseSchemaDataDataSizeNode,
                        saveTagMappingResultDataNode
                    )).ELSE(WHEN(
                        recordParseSchemaDataDataSizeNode.tag("one"),
                        saveUnmatchedDataToDBNode
                    ))
                )).ELSE(WHEN(
                    recordParseSchemaDataDataSizeNode.tag("one"),
                    saveUnmatchedDataToDBNode
                ))
            )).ELSE(WHEN(
                recordParseSchemaDataDataSizeNode.tag("one"),
                saveUnmatchedDataToDBNode
            ))
        ));
    </chain>



    <!-- 监听3个接口数据项，完成合并后推送到数据清洗服务 -->
    <chain name="listening_unprocessed_dataset_flow">
        IF(loadUnprocessedDatasetNode, THEN(
            sendDataToDataNode,
            modifyMergingResultDataStatusNode,
            removeMarkDataStatusNode
        ));
    </chain>
</flow>

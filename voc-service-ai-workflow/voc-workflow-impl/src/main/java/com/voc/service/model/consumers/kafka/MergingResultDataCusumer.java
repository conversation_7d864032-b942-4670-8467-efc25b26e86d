package com.voc.service.model.consumers.kafka;

import com.voc.service.model.api.IAnalysisService;
import com.voc.service.model.api.IMergeStatusRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CatListener
 * @createTime 2024年03月14日 9:46
 * @Copyright cuick
 */
@Component("mergingResultData.cusumer.kafka")
public class MergingResultDataCusumer {

    @Autowired
    IMergeStatusRecordService mergeStatusRecordService;
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    IAnalysisService iAnalysisService;
    static final String state_update_key = "ai-workflow::state_update_pop::{}::".concat(MergingResultDataCusumer.class.getSimpleName());

    /*@KafkaListener(topics = {MergingResultDataProducer.TOPIC_EVENT_MODIFY}, groupId = "voc-ai-flow")
    public void onMessage(String message) {
        log.debug(">>>>>>> 收到 {} 的请求 <<<<<<<<<<<<<<", message);
        final MessageDTO dto;
        try {
            dto = JSONUtil.toBean(message, MessageDTO.class);
            if (ObjUtil.isNull(dto)) {
                log.error("dto {}", dto);
                return;
            }
            log.info(">>>>>>> 开始执行任务 <<<<<<<<<<<<<< {}", dto.getData());
            Assert.isTrue(ObjectUtil.isNotEmpty(dto.getData()), "getProcessingDataset cannot be empty");
            Assert.isTrue(ObjectUtil.isNotEmpty(dto.getType()), "getType cannot be empty");
            final List<String> ids = JSONUtil.toList(JSONUtil.parseArray(dto.getData()), String.class);
            final MessageExt statusExt = dto.getExt().stream().filter(ext -> "status".equals(ext.getKey())).findAny().get();
            Assert.isTrue(ObjectUtil.isNotEmpty(statusExt.getValue()), "statusExt.getValue() cannot be empty");
            log.info("ids {}, status: ", ids, statusExt.getValue());

            if ("opinion".equals(dto.getType())) {
                mergeStatusRecordService.updateLabeledDataSize(new HashSet<>(ids), NumberUtil.parseInt(String.valueOf(statusExt.getValue())));
            } else if ("scenario".equals(dto.getType())) {
                mergeStatusRecordService.updateScenariosDataSize(new HashSet<>(ids), NumberUtil.parseInt(String.valueOf(statusExt.getValue())));
            } else if ("brand".equals(dto.getType())) {
                mergeStatusRecordService.updateBrandDataSize(new HashSet<>(ids), NumberUtil.parseInt(String.valueOf(statusExt.getValue())));
            }

//            boolean isExist = iAnalysisService.isExistUnprocessedDataset(data.getVoiceId());
//            if (!isExist) {
            //先放入redis set中， 在batchAction()中定时获取修改状态的数据集完成批量更新
//            redisTemplate.opsForList().rightPush(StrUtil.format(state_update_key, dto.getType()), data);
//            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }*/

    /*@Scheduled(cron = "0/10 * * * * ?")
    public void batchAction() {
        try {
            final int popCount = 100;

            List<CompletableFuture<Void>> futureList = new CopyOnWriteArrayList<>();
            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                final List<MergeStatusRecordModel> opinionList = redisTemplate.opsForList().leftPop(StrUtil.format(state_update_key, "opinion"), popCount);
                if (CollUtil.isNotEmpty(opinionList)) {
                    mergeStatusRecordService.updateLabeledDataSizeDB(opinionList,);
                }
                return null;
            })));

            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                final List<MergeStatusRecordModel> scenarioList = redisTemplate.opsForList().leftPop(StrUtil.format(state_update_key, "scenario"), popCount);
                if (CollUtil.isNotEmpty(scenarioList)) {
                    mergeStatusRecordService.updateScenariosDataSizeDB(scenarioList);
                }

                return null;
            })));

            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                final List<MergeStatusRecordModel> brandList = redisTemplate.opsForList().leftPop(StrUtil.format(state_update_key, "brand"), popCount);
                if (CollUtil.isNotEmpty(brandList)) {
                    mergeStatusRecordService.updateBrandDataSizeDB(brandList);
                }

                return null;
            })));

            try {
                CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).get(120, java.util.concurrent.TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new RuntimeException(e);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }*/
}


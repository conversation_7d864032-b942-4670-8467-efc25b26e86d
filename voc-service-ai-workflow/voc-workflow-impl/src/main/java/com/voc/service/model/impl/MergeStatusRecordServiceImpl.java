package com.voc.service.model.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.common.util.GeometricProgression;
import com.voc.service.common.util.ServiceContextHolder;
import com.voc.service.model.api.IAnalysisService;
import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.entity.MergeStatusRecordEntity;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.MergeStatusRecordMapper;
import com.voc.service.model.model.MergeStatusRecordModel;
import com.voc.service.model.model.MergingResultDataModel;
import com.voc.service.model.producers.kafka.MergingResultDataProducer;
import com.voc.service.model.producers.kafka.ModelResultProducer;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Title: MergeStatusRecordServiceImpl
 * @Package: com.voc.service.model.impl
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/5 17:15
 * @Version:1.0
 */
@Service
public class MergeStatusRecordServiceImpl extends ServiceImpl<MergeStatusRecordMapper, MergeStatusRecordEntity>
        implements IMergeStatusRecordService {
    private static final Logger log = LoggerFactory.getLogger(MergeStatusRecordServiceImpl.class);
    @Autowired
    ModelConvertMapperService modelConvertMapperService;
    @Autowired
    MergingResultDataProducer mergingResultDataProducer;
    @Autowired
    ModelResultProducer modelResultProducer;
    @Autowired
    IAnalysisService analysisService;
    @Autowired
    RedisTemplate redisTemplate;
    @Resource
    private RedissonClient redissonClient;

    @CreateCache(area = "VDP", name = ":",  expire = 60 * 1, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.REMOTE)
    private Cache<String, MergeStatusRecordEntity> recordModMergeStatusCache;
    private static final String MOD_MERGE_STATUS_RECORD_KEY = "{}:mod_merge_status_record:{}";

    private String getRecordModMergeStatusBrandsKey(Object... params){
        return StrUtil.format(MOD_MERGE_STATUS_RECORD_KEY, ServiceContextHolder.getSystemId(),params);
    }

    @Override
    public List<MergeStatusRecordModel> getModelListCahce(Set<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return List.of();
        }

        List<MergeStatusRecordEntity> list = this.getEntityListByCache(ids);
        return list.parallelStream().map(entity -> modelConvertMapperService.cenvertToMergeStatusRecordModel(entity))
                .filter(ObjectUtils::isNotEmpty)
                .collect(Collectors.toList());
    }

    @Override
    public List<MergeStatusRecordModel> getMergeStatusRecordList(Set<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        QueryWrapper<MergeStatusRecordEntity> wrapper = new QueryWrapper<>();
        wrapper.in("original_id", ids);
        wrapper.eq("done", 1);
        List<MergeStatusRecordEntity> mergeStatusRecordEntities = this.baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(mergeStatusRecordEntities)) {
            return new ArrayList<>();
        }
        return mergeStatusRecordEntities.parallelStream().map(entity -> modelConvertMapperService.cenvertToMergeStatusRecordModel(entity))
                .filter(ObjectUtils::isNotEmpty)
                .collect(Collectors.toList());
    }

    public void preLoadDatase(final Set<String> ids) {
        this.getEntityListByCache(ids);
    }

    private List<MergeStatusRecordEntity> getEntityListByCache(final Set<String> ids) {
        List<MergeStatusRecordEntity> list = new CopyOnWriteArrayList<>();
        ids.stream().forEach(id -> {
            MergeStatusRecordEntity entity = this.getEntityCache(id);
            if (ObjectUtils.isNotEmpty(entity)) {
                list.add(entity);
            }
        });
        return list;
    }

    private MergeStatusRecordEntity getEntityCache(String id) {
        MergeStatusRecordEntity entity = recordModMergeStatusCache.computeIfAbsent(id, (key) -> {
            QueryWrapper<MergeStatusRecordEntity> wrapper = new QueryWrapper<>();
            wrapper.in("new_id", id);
            return this.baseMapper.selectOne(wrapper);
        });
        return entity;
    }

    @Override
    public void push(String id, MergeStatusRecordModel model) {
        MergeStatusRecordEntity entity = modelConvertMapperService.cenvertToMergeStatusRecordEntity(model);

        recordModMergeStatusCache.put(this.getRecordModMergeStatusBrandsKey(id), entity);
    }

    public void push(String id, MergeStatusRecordEntity entity) {
        recordModMergeStatusCache.put(this.getRecordModMergeStatusBrandsKey(id), entity);
    }

    /*private void putAllCache(Map<String, MergeStatusRecordEntity> maps) {
        cache.putAll(maps);
    }

    private void removeCache(final Set<String> ids) {
        cache.removeAll(ids);
    }*/

    @Override
    public void updateLabeledDataSize(final Set<String> ids, final int status) throws Exception {

        if (CollUtil.isEmpty(ids)) {
            return;
        }
        for (String id : ids) {
            RLock rlock = redissonClient.getLock(id);
            try {
                if (!rlock.isLocked()) {
                    rlock.lock();

                    MergeStatusRecordEntity entity = this.getEntityCache(id);
                    if (ObjectUtils.isNotEmpty(entity) && entity.getLabelNum() == -1) {
                        entity.setLabelNum(status);
                        entity.setUpdateTime(LocalDateTime.now());
                        this.push(id, entity);
                    }
                    if (entity.getLabelNum() != -1 && entity.getScenarioNum() != -1 && entity.getBrandCarNum() != -1) {
                        entity.setStatus(entity.getPresetStatus());
                        this.push(id, entity);
                        mergingResultDataProducer.pushMergingResultData(MessageDTO.builder().source("").data(List.of(entity)).build());
                    }
                }
            } catch (Exception e) {
                log.error("error:{} ids: {}", e.getMessage(), ids);
                log.error(e.getMessage(), e);
            } finally {
                if (rlock.isHeldByCurrentThread()) {
                    rlock.unlock();
                }

            }
        }
        /*ids.stream().forEach(id -> {
            synchronized (id) {
                MergeStatusRecordEntity entity = this.getEntityCache(id);
                entity.setLabelNum(status);
                entity.setUpdateTime(LocalDateTime.now());
                this.push(id, entity);

                if (entity.getLabelNum() != -1 && entity.getScenarioNum() != -1 && entity.getBrandCarNum() != -1) {
                    entity.setStatus(entity.getPresetStatus());
                    mergingResultDataProducer.pushMergingResultData(MessageDTO.builder().source("").data(Arrays.asList(entity)).build());
                }
            }
        });*/

        /*List<MergeStatusRecordEntity> list = this.getEntityListByCache(ids);
        if (CollUtil.isEmpty(list)) {
            log.warn("redis 和 db 无次集合id数据集 {}", ids);
            return;
        }

        Map<String, MergeStatusRecordEntity> map = new ConcurrentReferenceHashMap<>();
        map.putAll(list.stream().collect(Collectors.toMap(MergeStatusRecordEntity::getNewId, entity -> entity, (v1, v2) -> v1)));

        //插入数据
        ids.stream().forEach(id -> {
            MergeStatusRecordEntity entity = map.get(id);
            entity.setLabelNum(status);
            entity.setUpdateTime(LocalDateTime.now());
        });
        //放入缓存
        this.putAllCache(map);


        //满足条件部分数据更新db
        final List<MergeStatusRecordEntity> entityList = map.values().stream()
                .filter(e -> e.getLabelNum() != -1 && e.getScenarioNum() != -1 && e.getBrandCarNum() != -1)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(entityList)) {
            //满足条件部分数据更新db
            final List<MergeStatusRecordEntity> doneList = entityList.stream().map(e -> {
                e.setStatus(e.getPresetStatus());
                return e;
            }).collect(Collectors.toList());
            mergingResultDataProducer.pushMergingResultData(MessageDTO.builder().source("").data(doneList).build());
            //并删除redis
            final Set<String> delRedisKey = entityList.stream().map(MergeStatusRecordEntity::getNewId).collect(Collectors.toSet());
            this.removeCache(delRedisKey);
//            redisTemplate.delete(delRedisKey);
        }*/
    }



    /* public void updateLabeledDataSizeDB(Set<String> ids,int status) {
     *//*final Map<Integer, List<MergeStatusRecordModel>> map = modelList.stream().collect(Collectors.groupingBy(MergeStatusRecordModel::getLabelNum));
        map.keySet().stream().forEach(count -> {
            List<MergeStatusRecordModel> list = map.get(count);
            final Set<String> md5List = list.stream()
                    .filter(model -> StrUtil.isNotBlank(model.getVoiceId()))
                    .filter(model -> StrUtil.isNotBlank(model.getOriginalId()))
                    .map(MergeStatusRecordModel::getVoiceId)
//                    .map(model -> DigestUtil.md5Hex(model.getVoiceId().concat(model.getOriginalId())))
                    .collect(Collectors.toSet());
            log.debug("md5List {}",md5List);
            log.info("labelNum 更新状态数量： {}",md5List.size());
            UpdateWrapper<MergeStatusRecordEntity> wrapper = new UpdateWrapper<>();
            wrapper.lambda().in(MergeStatusRecordEntity::getVoiceId, md5List);
            wrapper.lambda().set(MergeStatusRecordEntity::getLabelNum, count);
            wrapper.lambda().set(MergeStatusRecordEntity::getUpdateTime, LocalDateTime.now());
            this.update(wrapper);
        });*//*
        UpdateWrapper<MergeStatusRecordEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().in(MergeStatusRecordEntity::getVoiceId, ids);
        wrapper.lambda().set(MergeStatusRecordEntity::getLabelNum, status);
        wrapper.lambda().set(MergeStatusRecordEntity::getUpdateTime, LocalDateTime.now());
        this.update(wrapper);
    }*/

    @Override
    public void updateScenariosDataSize(final Set<String> ids, final int status) throws Exception {

//        List<MergeStatusRecordEntity> list = this.getEntityListByCache(ids);
        /*if (CollUtil.isEmpty(list)) {
            log.warn("redis 和 db 无次集合id数据集 {}", ids);
            return;
        }*/
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        for (String id : ids) {
            RLock rlock = redissonClient.getLock(id);
            try {
                if (!rlock.isLocked()) {
                    rlock.lock();

                    MergeStatusRecordEntity entity = this.getEntityCache(id);
                    if (ObjectUtils.isNotEmpty(entity) && entity.getScenarioNum() == -1) {
                        entity.setScenarioNum(status);
                        entity.setUpdateTime(LocalDateTime.now());
                        this.push(id, entity);
                    }
                    if (entity.getLabelNum() != -1 && entity.getScenarioNum() != -1 && entity.getBrandCarNum() != -1) {
                        entity.setStatus(entity.getPresetStatus());
                        this.push(id, entity);
                        mergingResultDataProducer.pushMergingResultData(MessageDTO.builder().source("").data(Arrays.asList(entity)).build());
                    }
                }
            } catch (Exception e) {
                log.error("error:{} ids: {}", e.getMessage(), ids);
                log.error(e.getMessage(), e);
            } finally {
                if (rlock.isHeldByCurrentThread()) {
                    rlock.unlock();
                }

            }
        }

        /*ids.stream().forEach(id -> {
            synchronized (id) {
                MergeStatusRecordEntity entity = this.getEntityCache(id);
                entity.setScenarioNum(status);
                entity.setUpdateTime(LocalDateTime.now());
                this.push(id, entity);

                if (entity.getLabelNum() != -1 && entity.getScenarioNum() != -1 && entity.getBrandCarNum() != -1) {
                    entity.setStatus(entity.getPresetStatus());
                    mergingResultDataProducer.pushMergingResultData(MessageDTO.builder().source("").data(Arrays.asList(entity)).build());
                }
            }
        });*/


        /*//插入数据
        ids.stream().forEach(id -> {
            MergeStatusRecordEntity entity = map.get(id);
            entity.setScenarioNum(status);
            entity.setUpdateTime(LocalDateTime.now());
        });
        //放入缓存
        this.putAllCache(map);
//        List<MergeStatusRecordEntity> list2 = this.getEntityListByCache(ids);

        //满足条件部分数据更新db
        final List<MergeStatusRecordEntity> entityList = map.values().stream()
                .filter(e -> e.getLabelNum() != -1 && e.getScenarioNum() != -1 && e.getBrandCarNum() != -1)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(entityList)) {
            //满足条件部分数据更新db
            final List<MergeStatusRecordEntity> doneList = entityList.stream().map(e -> {
                e.setStatus(e.getPresetStatus());
                return e;
            }).collect(Collectors.toList());
            mergingResultDataProducer.pushMergingResultData(MessageDTO.builder().source("").data(doneList).build());
            //并删除redis
            final Set<String> delRedisKey = entityList.stream().map(MergeStatusRecordEntity::getNewId).collect(Collectors.toSet());
            this.removeCache(delRedisKey);
//            redisTemplate.delete(delRedisKey);
        }*/
    }

    /*public void updateScenariosDataSizeDB(Set<String> ids,int status) {
     *//*final Map<Integer, List<MergeStatusRecordModel>> map = modelList.stream().collect(Collectors.groupingBy(MergeStatusRecordModel::getScenarioNum));
        map.keySet().stream().forEach(count -> {
            List<MergeStatusRecordModel> list = map.get(count);
            final Set<String> md5List = list.stream()
                    .filter(model -> StrUtil.isNotBlank(model.getVoiceId()))
                    .filter(model -> StrUtil.isNotBlank(model.getOriginalId()))
                    .map(MergeStatusRecordModel::getVoiceId)
//                    .map(model -> DigestUtil.md5Hex(model.getVoiceId().concat(model.getOriginalId())))
                    .collect(Collectors.toSet());
            log.debug("md5List {}",md5List);
            log.info("scenarioNum 更新状态数量： {}",md5List.size());
            UpdateWrapper<MergeStatusRecordEntity> wrapper = new UpdateWrapper<>();
            wrapper.lambda().in(MergeStatusRecordEntity::getVoiceId, md5List);
            wrapper.lambda().set(MergeStatusRecordEntity::getScenarioNum, count);
            wrapper.lambda().set(MergeStatusRecordEntity::getUpdateTime, LocalDateTime.now());
            this.update(wrapper);
        });*//*

        UpdateWrapper<MergeStatusRecordEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().in(MergeStatusRecordEntity::getVoiceId, ids);
        wrapper.lambda().set(MergeStatusRecordEntity::getScenarioNum, status);
        wrapper.lambda().set(MergeStatusRecordEntity::getUpdateTime, LocalDateTime.now());
        this.update(wrapper);

    }
*/
    @Override
    public void updateBrandDataSize(final Set<String> ids, final int status) throws Exception {

        if (CollUtil.isEmpty(ids)) {
            return;
        }

        for (String id : ids) {
            RLock rlock = redissonClient.getLock(id);
            try {
                if (!rlock.isLocked()) {
                    rlock.lock();

                    MergeStatusRecordEntity entity = this.getEntityCache(id);
                    if (ObjectUtils.isNotEmpty(entity) && entity.getBrandCarNum() == -1) {
                        entity.setBrandCarNum(status);
                        entity.setUpdateTime(LocalDateTime.now());
                        this.push(id, entity);
                    }
                    if (entity.getLabelNum() != -1 && entity.getScenarioNum() != -1 && entity.getBrandCarNum() != -1) {
                        entity.setStatus(entity.getPresetStatus());
                        this.push(id, entity);
                        mergingResultDataProducer.pushMergingResultData(MessageDTO.builder().source("").data(List.of(entity)).build());
                    }
                }
            } catch (Exception e) {
                log.error("error:{} ids: {}", e.getMessage(), ids);
                log.error(e.getMessage(), e);
            } finally {
                if (rlock.isHeldByCurrentThread()) {
                    rlock.unlock();
                }

            }
        }

        /*ids.stream().forEach(id -> {
            synchronized (id) {
                MergeStatusRecordEntity entity = this.getEntityCache(id);
                entity.setBrandCarNum(status);
                entity.setUpdateTime(LocalDateTime.now());
                this.push(id, entity);

                if (entity.getLabelNum() != -1 && entity.getScenarioNum() != -1 && entity.getBrandCarNum() != -1) {
                    entity.setStatus(entity.getPresetStatus());
                    mergingResultDataProducer.pushMergingResultData(MessageDTO.builder().source("").data(Arrays.asList(entity)).build());
                }
            }
        });*/


        /*List<MergeStatusRecordEntity> list = this.getEntityListByCache(ids);
        if (CollUtil.isEmpty(list)) {
            log.warn("redis 和 db 无次集合id数据集 {}", ids);
            return;
        }

        Map<String, MergeStatusRecordEntity> map = new ConcurrentReferenceHashMap<>();
        map.putAll(list.stream().collect(Collectors.toMap(MergeStatusRecordEntity::getNewId, entity -> entity, (v1, v2) -> v1)));

        //插入数据
        ids.stream().forEach(id -> {
            MergeStatusRecordEntity entity = map.get(id);
            entity.setBrandCarNum(status);
            entity.setUpdateTime(LocalDateTime.now());
        });
        //放入缓存
        this.putAllCache(map);
//        List<MergeStatusRecordEntity> list2 = this.getEntityListByCache(ids);

        //满足条件部分数据更新db
        final List<MergeStatusRecordEntity> entityList = map.values().stream()
                .filter(e -> e.getLabelNum() != -1 && e.getScenarioNum() != -1 && e.getBrandCarNum() != -1)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(entityList)) {
            //满足条件部分数据更新db
            final List<MergeStatusRecordEntity> doneList = entityList.stream().map(e -> {
                e.setStatus(e.getPresetStatus());
                return e;
            }).collect(Collectors.toList());
            mergingResultDataProducer.pushMergingResultData(MessageDTO.builder().source("").data(doneList).build());
            //并删除redis
            final Set<String> delRedisKey = entityList.stream().map(MergeStatusRecordEntity::getNewId).collect(Collectors.toSet());
            this.removeCache(delRedisKey);
//            redisTemplate.delete(delRedisKey);
        }*/
    }

    /*public void updateBrandDataSizeDB(Set<String> ids,int status) {
     *//*final Map<Integer, List<MergeStatusRecordModel>> map = modelList.stream().collect(Collectors.groupingBy(MergeStatusRecordModel::getBrandCarNum));
        map.keySet().stream().forEach(count -> {
            List<MergeStatusRecordModel> list = map.get(count);
            final Set<String> md5List = list.stream()
                    .filter(model -> StrUtil.isNotBlank(model.getVoiceId()))
                    .filter(model -> StrUtil.isNotBlank(model.getOriginalId()))
                    .map(MergeStatusRecordModel::getVoiceId)
//                    .map(model -> DigestUtil.md5Hex(model.getVoiceId().concat(model.getOriginalId())))
                    .collect(Collectors.toSet());
            log.debug("md5List {}",md5List);
            log.info("brandCarNum 更新状态数量： {}",md5List.size());
            UpdateWrapper<MergeStatusRecordEntity> wrapper = new UpdateWrapper<>();
            wrapper.lambda().in(MergeStatusRecordEntity::getVoiceId, md5List);
            wrapper.lambda().set(MergeStatusRecordEntity::getBrandCarNum, count);
            wrapper.lambda().set(MergeStatusRecordEntity::getUpdateTime, LocalDateTime.now());
            this.update(wrapper);
        });*//*
        UpdateWrapper<MergeStatusRecordEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().in(MergeStatusRecordEntity::getVoiceId, ids);
        wrapper.lambda().set(MergeStatusRecordEntity::getBrandCarNum, status);
        wrapper.lambda().set(MergeStatusRecordEntity::getUpdateTime, LocalDateTime.now());
        this.update(wrapper);
    }*/

    @Override
    public int saveMergeStatusRecord(List<MergeStatusRecordModel> mergeStatusRecordModelList) {
        if (CollectionUtil.isEmpty(mergeStatusRecordModelList)) {
            return 0;
        }
        List<MergeStatusRecordEntity> mergeStatusRecordEntities = modelConvertMapperService.cenvertToMergeStatusRecordEntityList(mergeStatusRecordModelList);
        mergingResultDataProducer.pushMergingResultData(MessageDTO.builder().source("").data(mergeStatusRecordEntities).build());
        return mergeStatusRecordModelList.size();
    }

    @Override
    public List<MergingResultDataModel> findUnpushedData(int pushAnalysisServiceMqDatasetSize) {
        if (pushAnalysisServiceMqDatasetSize < 10) {
            pushAnalysisServiceMqDatasetSize = 500;
        }
        List<MergeStatusRecordEntity> completedDataList = this.baseMapper.findUnpushedData(pushAnalysisServiceMqDatasetSize);
        return modelConvertMapperService.cenvertToMergingResultDataModellList(completedDataList);
    }


    @Override
    public int modifyToDoneDB(Set<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return 0;
        }
        QueryWrapper<MergeStatusRecordEntity> wrapper = new QueryWrapper<>();
        wrapper.in("original_id", ids);
        List<MergeStatusRecordEntity> mergeStatusRecordEntities = this.baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(mergeStatusRecordEntities)) {
            return 0;
        }
        mergeStatusRecordEntities.forEach(entity -> entity.setDone(1));
        log.info("mergeStatusRecordEntities.modifyToDoneDB {}", mergeStatusRecordEntities.size());
        mergingResultDataProducer.pushMergingResultData(MessageDTO.builder().source("").data(mergeStatusRecordEntities).build());
        return mergeStatusRecordEntities.size();
    }

    @Override
    public int modifyToStatusDB(final Set<String> ids) {

        /*List<MergeStatusRecordEntity> list = this.getEntityListByCache(ids);
        if (CollUtil.isEmpty(list)) {
            log.warn("redis 和 db 无次集合id数据集 {}", ids);
            return 0;
        }

        Map<String, MergeStatusRecordEntity> map = new ConcurrentReferenceHashMap<>();
        map.putAll(list.stream().collect(Collectors.toMap(MergeStatusRecordEntity::getNewId, entity -> entity, (v1, v2) -> v1)));

        //插入数据
        ids.stream().forEach(id -> {
            MergeStatusRecordEntity entity = map.get(id);
            entity.setStatus(entity.getPresetStatus());
            entity.setUpdateTime(LocalDateTime.now());
        });
        //放入缓存
        this.putAllCache(map);
//        List<MergeStatusRecordEntity> list2 = this.getEntityListByCache(ids);

        //满足条件部分数据更新db
        final List<MergeStatusRecordEntity> entityList = map.values().stream()
                .filter(e -> e.getLabelNum() != -1 && e.getScenarioNum() != -1 && e.getBrandCarNum() != -1 && e.getStatus() == e.getPresetStatus())
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(entityList)) {
            //满足条件部分数据更新db
            final  List<MergeStatusRecordEntity> doneList = entityList.stream().map(e -> {
                e.setStatus(e.getPresetStatus());
                return e;
            }).collect(Collectors.toList());
            mergingResultDataProducer.pushMergingResultData(MessageDTO.builder().source("").data(doneList).build());
            //并删除redis
            final Set<String> delRedisKey = entityList.stream().map(MergeStatusRecordEntity::getNewId).collect(Collectors.toSet());
            this.removeCache(delRedisKey);
//            redisTemplate.delete(delRedisKey);
        }*/
        return 1;
    }

    @Override
    public void sendModifyStatusEvent(List<String> ids) {
        modelResultProducer.pushModifyEvent(MessageDTO.builder().data(ids).build());
    }

    @Override
    public Set<String> updateDataStatus(int status, Set<String> ids, int conditionStatus) {
        if (CollUtil.isEmpty(ids)) {
            return Set.of();
        }

        //记录满足条件的值集合
        Set<String> doneIds = new ConcurrentHashSet<>();
        ids.stream().forEach(id -> {
            MergeStatusRecordEntity entity = this.getEntityCache(id);
            final int oldVal = entity.getStatus();

            final Set<Integer> statusList = GeometricProgression.split(entity.getStatus());
            final int newVal;
            if (statusList.contains(status)) {
                newVal = oldVal;
            } else {
                newVal = oldVal + status;
            }
            entity.setStatus(newVal);
            this.push(id, entity);

            // 获取满足条件的key集合
            if (entity.getStatus() == conditionStatus) {
                doneIds.add(id);
            }
        });

        log.debug("updateDataStatus:{},{},{},{},{}", status, ids, conditionStatus);
        return doneIds;
    }

    /**
     * 流程是否已执行完成
     */
    @Override
    public boolean isWorkFlowSuccessed(Set<String> voiceIds, int statusParam) {
        if (CollUtil.isEmpty(voiceIds)) {
            return false;
        }
        try {
            List<MergeStatusRecordEntity> list = this.getEntityListByCache(voiceIds);
            final List<Integer> values = list.stream().map(MergeStatusRecordEntity::getStatus).toList();
            final List<Integer> statusList = values.stream().filter(ObjUtil::isNotNull).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(statusList) && statusList.size() == voiceIds.size()) {
                return statusList.stream().filter(ObjUtil::isNotNull).allMatch(status -> statusParam == status);
            }
        } catch (Exception e) {
            log.info("查询redis异常:", e);
            throw new RuntimeException(e);
        }
        return false;
    }

    @Override
    public void deleteDataStatus(Set<String> voiceIds) {
        recordModMergeStatusCache.removeAll(voiceIds.stream().map(id ->this.getRecordModMergeStatusBrandsKey(id)).collect(Collectors.toSet()));
    }
}

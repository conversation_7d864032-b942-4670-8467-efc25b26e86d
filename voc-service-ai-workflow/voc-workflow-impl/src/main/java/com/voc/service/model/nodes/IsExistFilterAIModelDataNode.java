package com.voc.service.model.nodes;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.voc.service.model.model.ModLlmReturnResultInfoRecordModel;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "isExistFilterAIModelDataNode", name = "过滤AI返回结果集内垃圾数据节点")
public class IsExistFilterAIModelDataNode extends AbstractNodeIf {


    private static final Logger log = LoggerFactory.getLogger(IsExistFilterAIModelDataNode.class);

    @Override
    public boolean processIf() throws Exception {
        try {
            AIResultDataContext context = this.getRequestData();
            Map<Integer, Set<String>> maps = new HashMap<>();

            final List<ModLlmReturnResultInfoRecordModel> filter1 = context.getBrandCarDataList().stream()
                    .filter(f -> (StringUtils.isBlank(f.getSubject()) || "NA".equalsIgnoreCase(f.getSubject()))
                            || (StringUtils.isBlank(f.getDescription()) || "NA".equalsIgnoreCase(f.getDescription())))
                    .collect(Collectors.toList());
            final Set<String> filterIds1 = filter1.stream().map(ModLlmReturnResultInfoRecordModel::getNewId).collect(Collectors.toSet());
            context.setBrandCarDataList(context.getBrandCarDataList().stream()
                    .filter(m -> !filterIds1.contains(m.getNewId())).collect(Collectors.toList()));
            if (CollUtil.isNotEmpty(filter1)) {
                maps.put(2, filterIds1);
            }

            final List<ModLlmReturnResultInfoRecordModel> filter2 = context.getOpinionDataList().stream()
                    .filter(f -> (StringUtils.isBlank(f.getSubject()) || "NA".equalsIgnoreCase(f.getSubject()))
                            || (StringUtils.isBlank(f.getDescription()) || "NA".equalsIgnoreCase(f.getDescription())))
                    .collect(Collectors.toList());
            final Set<String> filterIds2 = filter2.stream().map(ModLlmReturnResultInfoRecordModel::getNewId).collect(Collectors.toSet());
            context.setOpinionDataList(context.getOpinionDataList().stream()
                    .filter(m -> !filterIds2.contains(m.getNewId())).collect(Collectors.toList()));
            if (CollUtil.isNotEmpty(filter2)) {
                maps.put(4, filterIds2);
            }


            final List<ModLlmReturnResultInfoRecordModel> filter3 = context.getScenarioDataList().stream()
                    .filter(f -> StringUtils.isBlank(f.getScenario()) || "NA".equalsIgnoreCase(f.getScenario()))
                    .collect(Collectors.toList());
            final Set<String> filterIds3 = filter3.stream().map(ModLlmReturnResultInfoRecordModel::getNewId).collect(Collectors.toSet());
            context.setScenarioDataList(context.getScenarioDataList().stream()
                    .filter(m -> !filterIds3.contains(m.getNewId())).collect(Collectors.toList()));
            if (CollUtil.isNotEmpty(filter3)) {
                maps.put(8, filterIds3);
            }
            if (MapUtil.isNotEmpty(maps)) {
                this.sendPrivateDeliveryData("modifyResultDataStatusNode", maps);
                return true;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }

}

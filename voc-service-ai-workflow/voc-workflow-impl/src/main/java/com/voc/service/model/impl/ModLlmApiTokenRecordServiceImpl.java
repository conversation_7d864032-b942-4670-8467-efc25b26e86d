package com.voc.service.model.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.model.api.IModLlmApiTokenRecordService;
import com.voc.service.model.entity.ModLlmApiTokenRecord;
import com.voc.service.model.mapper.ModLlmApiTokenRecordMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【mod_llm_api_token_record(智谱apikeywg记录表)】的数据库操作Service实现
 * @createDate 2024-08-01 13:45:36
 */
@Service
public class ModLlmApiTokenRecordServiceImpl extends ServiceImpl<ModLlmApiTokenRecordMapper, ModLlmApiTokenRecord>
        implements IModLlmApiTokenRecordService {

}





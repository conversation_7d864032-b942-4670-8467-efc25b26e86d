package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollectionUtil;
import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.model.MergeStatusRecordModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @Title: saveMergeResultAnalysisNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "saveMergeResultStatusAnalysisNode", name = "保存合并表数据节点")
public class SaveMergeResultAnalysisNode extends AbstractNode {

    private static final Logger log = LoggerFactory.getLogger(SaveMergeResultAnalysisNode.class);
    @Autowired
    IMergeStatusRecordService iMergeStatusRecordService;

    @Override
    public void process() throws Exception {
        try {
            ThirdPartyAIContext context = this.getRequestData();
            List<MergeStatusRecordModel> mergeStatusRecordModelList = context.getMergeStatusRecordModelList();
            if (CollectionUtil.isEmpty(mergeStatusRecordModelList)) {
                return;
            }
            int count = iMergeStatusRecordService.saveMergeStatusRecord(mergeStatusRecordModelList);
            log.info("数据状态入库条数:{}", count);
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        ThirdPartyAIContext context = this.getRequestData();
        return true;
    }

}

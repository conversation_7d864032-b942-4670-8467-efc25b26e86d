package com.voc.service.model.nodes;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.model.api.*;
import com.voc.service.model.model.*;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.AnalysisDataContext;
import com.voc.service.model.producers.kafka.AnalysisProducer;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "sendDataToDataNode", name = "发送数据到数据清洗服务节点")
public class SendDataToDataNode extends AbstractNode {

    private static final Logger log = LoggerFactory.getLogger(SendDataToDataNode.class);
    @Autowired
    AnalysisProducer analysisProducer;

    @Autowired
    IModCaTagResultService iModCaTagResultService;

    @Autowired
    IModCarNotTagResultService iModCarNotTagResultService;

    @Autowired
    IModCarSceneResultService iModCarSceneResultService;

    @Autowired
    IModFtmBrandCarSeriesService iModFtmBrandCarSeriesService;

    @Autowired
    IModLlmReturnResultInfoRecordService iModLlmReturnResultInfoRecordService;

    @Autowired
    IModMetaDataAnalysisService iModMetaDataAnalysisService;


    @Override
    public void process() throws Exception {
        try {
            AnalysisDataContext context = this.getRequestData();
            Set<String> ids = context.getIds();
            log.info("要合并数据的ids:{}", ids);
            if (CollectionUtil.isEmpty(ids)) {
                return;
            }
            //已打标签数据
            List<ModCaTagResultModel> tagResultModelList = iModCaTagResultService.findTagResultModel(ids);
            Map<String, List<ModCaTagResultModel>> tagResultMap = tagResultModelList.stream().collect(Collectors.groupingBy(ModCaTagResultModel::getOriginalId));
            //未打标签数据
            List<ModCaNotTagResultModel> carNotTagResultList = iModCarNotTagResultService.findCarNotTagResult(ids);
            Map<String, List<ModCaNotTagResultModel>> notTagResultMap = carNotTagResultList.stream().collect(Collectors.groupingBy(ModCaNotTagResultModel::getOriginalId));
            //整车场景数据暂时没用到后期可能会做调整
            List<ModCarSceneResultModel> modCarSceneResultList = iModCarSceneResultService.findModCarSceneResult(ids);
            Map<String, ModCarSceneResultModel> modCarSceneResultMap = modCarSceneResultList.stream().collect(Collectors.toMap(ModCarSceneResultModel::getVoiceId, m -> m, (v1, v2) -> v1));
            //品牌车系数据
            List<ModFtmBrandCarSeriesModel> modFtmBrandCarSeriesList = iModFtmBrandCarSeriesService.findModFtmBrandCarSeries(ids);
            Map<String, List<ModFtmBrandCarSeriesModel>> modFtmBrandCarSeriesMap = modFtmBrandCarSeriesList.stream().collect(Collectors.groupingBy(ModFtmBrandCarSeriesModel::getOriginalId));
            //智谱返回结果数据
            List<ModLlmReturnResultInfoRecordModel> returnResultInfoRecordList = iModLlmReturnResultInfoRecordService.findReturnResultInfoRecord(ids);
            Map<String, ModLlmReturnResultInfoRecordModel> resultInfoRecordModelMap = returnResultInfoRecordList.stream().collect(Collectors.toMap(ModLlmReturnResultInfoRecordModel::getNewId, m -> m, (v1, v2) -> v1));
            Map<String, List<ModLlmReturnResultInfoRecordModel>> resultInfoRecordModelGroupMap = returnResultInfoRecordList.stream().collect(Collectors.groupingBy(ModLlmReturnResultInfoRecordModel::getOriginalId));

            List<ModMetaDataAnalysisModel> modMetaDataAnalysisList = iModMetaDataAnalysisService.findModMetaDataAnalysisList(new ArrayList<>(ids));
            Map<String, ModMetaDataAnalysisModel> metaDataAnalysisModelMap = modMetaDataAnalysisList.stream().collect(Collectors.toMap(ModMetaDataAnalysisModel::getNewId, Function.identity()));

            List<ResultData> resultList = new ArrayList<>();
            for (String originalId : ids) {
                ResultData resultData = new ResultData();
                List<AiData> aiDataList = new ArrayList<>();
                //初始化返回数据
                if (MapUtil.isNotEmpty(resultInfoRecordModelGroupMap) && resultInfoRecordModelGroupMap.containsKey(originalId)) {
                    List<ModLlmReturnResultInfoRecordModel> modelList = resultInfoRecordModelGroupMap.get(originalId);
                    for (ModLlmReturnResultInfoRecordModel returnResultInfoRecordModel : modelList) {
                        AiData aiData = new AiData();
                        aiData.setId(originalId);
                        if (MapUtil.isNotEmpty(metaDataAnalysisModelMap) && metaDataAnalysisModelMap.containsKey(originalId)) {
                            aiData.setSentence(metaDataAnalysisModelMap.get(originalId).getContent());
                        }
                        aiData.setVoiceId(returnResultInfoRecordModel.getNewId());
                        aiDataList.add(aiData);
                    }
                }
                //补充品牌和标签数据
                if (MapUtil.isNotEmpty(modFtmBrandCarSeriesMap) && modFtmBrandCarSeriesMap.containsKey(originalId)) {
                    List<ModFtmBrandCarSeriesModel> modFtmBrandCarSeriesModels = modFtmBrandCarSeriesMap.get(originalId);
                    Map<String, AiData> aiDataMap = aiDataList.stream().collect(Collectors.toMap(AiData::getVoiceId, m -> m, (v1, v2) -> v1));
                    for (ModFtmBrandCarSeriesModel brandCarSeriesModel : modFtmBrandCarSeriesModels) {
                        if (MapUtil.isNotEmpty(aiDataMap) && aiDataMap.containsKey(brandCarSeriesModel.getVoiceId())) {
                            AiData aiData = aiDataMap.get(brandCarSeriesModel.getVoiceId());
                            aiData.setBrand(brandCarSeriesModel.getBrand());
                            aiData.setCarSeries(brandCarSeriesModel.getCarSeries());
                        }
                    }
                }
                //补充标签数据
                if (MapUtil.isNotEmpty(tagResultMap) && tagResultMap.containsKey(originalId)) {
                    List<ModCaTagResultModel> modCaTagResultModels = tagResultMap.get(originalId);
                    Map<String, AiData> aiDataMap = aiDataList.stream().collect(Collectors.toMap(AiData::getVoiceId, Function.identity()));
                    Set<String> keywordList = new HashSet<>();
                    for (ModCaTagResultModel caTagResultModel : modCaTagResultModels) {
                        if (MapUtil.isNotEmpty(aiDataMap) && aiDataMap.containsKey(caTagResultModel.getVoiceId())) {
                            AiData aiData = aiDataMap.get(caTagResultModel.getVoiceId());
                            List<FaultsModel> faults = aiData.getFaults();
                            if (StringUtils.isNotBlank(caTagResultModel.getKeywords())) {
                                String modelKeywords = caTagResultModel.getKeywords();
                                List<String> list = JSON.parseObject(modelKeywords, new TypeReference<>() {
                                });
                                keywordList.addAll(list);
                            }
                            List<DimensionsModel> dimensions = aiData.getDimensions();
                            if (StringUtils.isNotBlank(caTagResultModel.getQTag())) {
                                FaultsModel faultsModel = new FaultsModel();
                                getLabel(faultsModel, caTagResultModel, resultInfoRecordModelMap, modCarSceneResultMap);
                                faults.add(faultsModel);
                            }
                            if (StringUtils.isNotBlank(caTagResultModel.getBTag())) {
                                DimensionsModel dimensionsModel = new DimensionsModel();
                                FaultsModel model = new FaultsModel();
                                getLabel(model, caTagResultModel, resultInfoRecordModelMap, modCarSceneResultMap);
                                BeanUtil.copyProperties(model, dimensionsModel);
                                dimensions.add(dimensionsModel);
                            }
                        }
                    }
                    //处理热词
                    if (CollectionUtil.isNotEmpty(keywordList) && MapUtil.isNotEmpty(aiDataMap)) {
                        Map<String, List<AiData>> dataMap = aiDataList.stream().collect(Collectors.groupingBy(AiData::getId));
                        List<AiData> aiData = dataMap.get(originalId);
                        AiData data = aiData.get(0);
                        data.setKeywords(new ArrayList<>(keywordList));
                    }
                }
                //补充未打标签数据
                if (MapUtil.isNotEmpty(notTagResultMap) && notTagResultMap.containsKey(originalId)) {
                    List<ModCaNotTagResultModel> modCaNotTagResultModels = notTagResultMap.get(originalId);
                    Map<String, List<AiData>> aiDataMap = aiDataList.stream().collect(Collectors.groupingBy(AiData::getId));
                    List<OpinionsModel> unmarkedOpinions = new ArrayList<>();
                    for (ModCaNotTagResultModel notTagResultModel : modCaNotTagResultModels) {
                        OpinionsModel opinionsModel = new OpinionsModel();
                        opinionsModel.setSubject(notTagResultModel.getSubject());
                        opinionsModel.setOpinionSentiment(notTagResultModel.getOpinionSentiment());
                        opinionsModel.setViewLabel(notTagResultModel.getViewLabel());
                        opinionsModel.setCarBodyLabel(notTagResultModel.getCarBodyLabel());
                        opinionsModel.setOpinion(notTagResultModel.getOpinion());
                        opinionsModel.setDesc(notTagResultModel.getDescription());
                        opinionsModel.setExtFields(notTagResultModel.getExtFields());
                        unmarkedOpinions.add(opinionsModel);
                    }
                    if (MapUtil.isNotEmpty(aiDataMap) && aiDataMap.containsKey(originalId)) {
                        List<AiData> aiData = aiDataMap.get(originalId);
                        AiData data = aiData.get(0);
                        data.setUnmarkedOpinions(unmarkedOpinions);
                    }
                }
                resultData.setResult(aiDataList);
                resultData.setClientId(returnResultInfoRecordList.get(0).getClientId());
                resultList.add(resultData);
            }
            AiMqResultDateModel aiMqResultDateModel = new AiMqResultDateModel();
            aiMqResultDateModel.setResult(resultList);
            log.info("合并数据完毕:{}", resultList.size());
            //推送MQ
            analysisProducer.pushData(MessageDTO.builder().data(aiMqResultDateModel).build());
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }


    private void getLabel(FaultsModel faultsModel, ModCaTagResultModel caTagResultModel, Map<String, ModLlmReturnResultInfoRecordModel> resultInfoRecordModelMap, Map<String, ModCarSceneResultModel> modCarSceneResultMap) {

        if (MapUtil.isNotEmpty(resultInfoRecordModelMap) && resultInfoRecordModelMap.containsKey(caTagResultModel.getVoiceId())) {
            ModLlmReturnResultInfoRecordModel returnResultInfoRecordModel = resultInfoRecordModelMap.get(caTagResultModel.getVoiceId());
            faultsModel.setDesc(returnResultInfoRecordModel.getDescription());
            faultsModel.setSubject(returnResultInfoRecordModel.getSubject());
        }
        if (MapUtil.isNotEmpty(modCarSceneResultMap) && modCarSceneResultMap.containsKey(caTagResultModel.getVoiceId())) {
            ModCarSceneResultModel modCarSceneResultModel = modCarSceneResultMap.get(caTagResultModel.getVoiceId());
            faultsModel.setScenario(modCarSceneResultModel.getSimScenario());
        }
        faultsModel.setLevelOne(caTagResultModel.getBusinessLabelTypeLevelFirst());
        faultsModel.setLevelTwo(caTagResultModel.getBusinessLabelTypeLevelSecond());
        faultsModel.setLevelThree(caTagResultModel.getBusinessLabelTypeLevelThree());
        faultsModel.setLevelFour(caTagResultModel.getBusinessLabelTypeLevelFour());
        faultsModel.setLevel(caTagResultModel.getFaultLevel());
        faultsModel.setTopic(caTagResultModel.getTopic());
        faultsModel.setOpinion(caTagResultModel.getOpinion());
        faultsModel.setSentiment(caTagResultModel.getSentiment());
        faultsModel.setIntention(caTagResultModel.getIntentionType());
        faultsModel.setExtFields(caTagResultModel.getExtFields());
    }

    @Override
    public boolean isAccess() {
        AnalysisDataContext context = this.getRequestData();
        Assert.isTrue(CollUtil.isNotEmpty(context.getIds()), "getIds cannot be empty");
        return true;
    }

}

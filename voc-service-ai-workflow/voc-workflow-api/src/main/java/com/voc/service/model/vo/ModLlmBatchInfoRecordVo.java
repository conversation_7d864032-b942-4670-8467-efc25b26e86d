package com.voc.service.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 大模型批次信息记录表
 *
 * @TableName mod_llm_batch_info_record
 */
@TableName(value = "mod_llm_batch_info_record")
@Data
public class ModLlmBatchInfoRecordVo implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "new_id", type = IdType.ASSIGN_UUID)
    private String newId;

    /**
     * 客户标识
     */
    private String clientId;

    /**
     * apikey
     */
    private String apiKey;

    /**
     * 接收处理标识
     */
    private String workId;

    /**
     * 上传文件的 ID
     */
    private String inputFileId;

    /**
     * 任务ID
     */
    private String batchId;

    /**
     * 成功执行请求的输出的文件ID
     */
    private String outputFileId;

    /**
     * 出现错误的请求的输出的文件ID
     */
    private String errorFileId;

    /**
     * 任务状态 validating:文件正在验证中，Batch 任务未开始
     * failed:文件未通过验证
     * in_progress:文件已成功验证，Batch 任务正在进行中
     * finalizing:Batch 任务已完成，结果正在准备中
     * completed:Batch 任务已完成，结果已准备好
     * expired:Batch 任务未能在24小时内完成
     * cancelling:Batch 任务正在取消中
     * cancelled:Batch 任务已取消
     */
    private String batchStatus;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String done;
    private Integer batchSize;
    private Integer errorNum;
    private String errorBatchId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}

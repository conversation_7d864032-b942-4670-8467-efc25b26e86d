package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.voc.service.common.util.GeometricProgression;
import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.model.MergeStatusRecordModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "addRedisStatusDataNode", name = "过滤掉正在运行的数据节点")
public class AddRedisStatusDataNode extends AbstractNode {

    private static final Logger log = LoggerFactory.getLogger(AddRedisStatusDataNode.class);
    int conditionStatus = 14;
    @Autowired
    IMergeStatusRecordService mergeStatusRecordService;

    @Override
    public void process() throws Exception {
        try {
            AIResultDataContext context = this.getRequestData();

            final List<String> idList = this.getPrivateDeliveryData();

            if (CollUtil.isEmpty(idList)) {
                return;
            }

            //新增运行key（运行中+新的） ， 类似加锁
//            final Set<String> allIds = iAnalysisService.addDataStatusList(new HashSet<>(idList));
//            log.info("allIds:{}", allIds);
            //读取
            List<MergeStatusRecordModel> list = mergeStatusRecordService.getModelListCahce(new HashSet<>(idList));
            final Map<String, Integer> dataStatusMap = list.stream()
                    .collect(Collectors.toMap(MergeStatusRecordModel::getNewId, MergeStatusRecordModel::getStatus,(v1, v2) -> v1));
            //声音需要执行的流程

            dataStatusMap.keySet().forEach(id -> {
                final Integer status = dataStatusMap.get(id);
                log.debug("id:{},status:{}", id, status);
                if (status != 0){
                    final Set<Integer> rs = GeometricProgression.split(status);
                    if (rs.contains(2)) {
                        context.setBrandCarDataList(context.getBrandCarDataList().stream()
                                .filter(m -> !m.getNewId().equals(id)).collect(Collectors.toList()));
                    }
                    if (rs.contains(4)) {
                        context.setOpinionDataList(context.getOpinionDataList().stream()
                                .filter(m -> !m.getNewId().equals(id)).collect(Collectors.toList()));
                    }
                    if (rs.contains(8)) {
                        context.setScenarioDataList(context.getScenarioDataList().stream()
                                .filter(m -> !m.getNewId().equals(id)).collect(Collectors.toList()));
                    }
                }

            });

            log.debug("过滤掉正在运行的数据节点 {}", dataStatusMap);
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        Assert.isTrue(ObjUtil.isNotNull(this.getRequestData()), "getRequestData cannot be empty");

        return true;
    }

}

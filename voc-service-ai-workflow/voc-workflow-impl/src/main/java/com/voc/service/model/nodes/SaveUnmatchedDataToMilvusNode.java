package com.voc.service.model.nodes;

import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ModelContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.RetryException;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "saveUnmatchedDataToMilvusNode", name = "保存未命中数据到Milvus节点")
public class SaveUnmatchedDataToMilvusNode extends AbstractNode {

    private static final Logger log = LoggerFactory.getLogger(SaveUnmatchedDataToMilvusNode.class);

    @Override
    public void process() throws Exception {
        try {
            log.info("saveUnmatchedDataToMilvusNode节点暂无实现");

           /* ModelContext context = this.getRequestData();
            if ("opinion".equalsIgnoreCase(context.getTag())) {

            } else if ("scenario".equalsIgnoreCase(context.getTag())) {

            } else {
                throw new Exception("未找到匹配tag值");
            }*/
            return;
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        ModelContext context = this.getRequestData();
//        Assert.isTrue(StrUtil.isNotBlank(context.getTag()), "getTag cannot be empty");
//        Assert.isTrue(CollUtil.isNotEmpty(context.getIds()), "getIds cannot be empty");


        return true;
    }

}

package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollUtil;
import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.model.ModFtmBrandCarSeriesModel;
import com.voc.service.model.model.ModWorkflowModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryException;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "recordMatchingSchemaDataSizeV2Node", name = "记录流程中数据集合数量节点")
public class RecordMatchingSchemaDataSizeV2Node extends AbstractNode {
    private static final Logger log = LoggerFactory.getLogger(RecordMatchingSchemaDataSizeV2Node.class);
    @Autowired
    IMergeStatusRecordService mergeStatusRecordService;

    @Override
    public void process() throws Exception {
        try {
            Map<Integer, List<ModFtmBrandCarSeriesModel>> maps = this.getPrivateDeliveryData();

            if (CollUtil.isEmpty(maps)) {
                return;
            }
            if (!maps.containsKey(2)) {
                log.error("无有效数据 {}", maps);
                return;
            }

            Map<Integer, List<ModWorkflowModel>> privateMap = new HashMap<>();
            final Set<String> voiceIdList = maps.get(2).stream().map(ModFtmBrandCarSeriesModel::getVoiceId).collect(Collectors.toSet());
            mergeStatusRecordService.updateBrandDataSize(voiceIdList, 1);

            for (ModFtmBrandCarSeriesModel model : maps.get(2)) {
                if (privateMap.containsKey(2)) {
                    privateMap.get(2).add(ModWorkflowModel.builder().voiceId(model.getVoiceId()).originalId(model.getOriginalId()).build());
                } else {
                    privateMap.put(2, new ArrayList<>(Arrays.asList(
                            ModWorkflowModel.builder().voiceId(model.getVoiceId()).originalId(model.getOriginalId()).build())
                    ));
                }
            }

            log.info("privateMap {}", privateMap);
            this.sendPrivateDeliveryData("modifyResultDataStatusNode", privateMap);
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        AIResultDataContext context = this.getRequestData();

        return true;
    }

}

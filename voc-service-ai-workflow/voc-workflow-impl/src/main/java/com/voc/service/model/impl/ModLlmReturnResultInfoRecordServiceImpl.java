package com.voc.service.model.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.model.api.IModLlmReturnResultInfoRecordService;
import com.voc.service.model.entity.ModLlmReturnResultInfoRecordEntity;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.ModLlmReturnResultInfoRecordMapper;
import com.voc.service.model.model.ModLlmReturnResultInfoRecordModel;
import com.voc.service.model.producers.kafka.ModelResultProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Service
public class ModLlmReturnResultInfoRecordServiceImpl extends ServiceImpl<ModLlmReturnResultInfoRecordMapper, ModLlmReturnResultInfoRecordEntity>
        implements IModLlmReturnResultInfoRecordService {

    @Autowired
    ModelConvertMapperService modelConvertMapperService;


    @Autowired
    ModelResultProducer modelResultProducer;


    @Override
    public synchronized int saveModLlmResultData(String clientId, List<ModLlmReturnResultInfoRecordModel> modelList) {
        if (CollectionUtil.isEmpty(modelList)) {
            return 0;
        }

        // 使用ConcurrentHashMap保证线程安全
        Map<String, List<ModLlmReturnResultInfoRecordModel>> listMap = modelList.stream()
                .collect(Collectors.groupingBy(
                        ModLlmReturnResultInfoRecordModel::getOriginalId,
                        ConcurrentHashMap::new,
                        Collectors.toList()
                ));

        List<ModLlmReturnResultInfoRecordEntity> modLlmReturnResultInfoRecordEntities = modelList.stream().map(model -> {
            List<ModLlmReturnResultInfoRecordModel> models = listMap.get(model.getOriginalId());
            ModLlmReturnResultInfoRecordEntity modLlmReturnResultInfoRecordEntity = new ModLlmReturnResultInfoRecordEntity();
            BeanUtil.copyProperties(model, modLlmReturnResultInfoRecordEntity);
            if (CollectionUtil.isNotEmpty(models)) {
                modLlmReturnResultInfoRecordEntity.setVoiceCount(models.size());
            }
            return modLlmReturnResultInfoRecordEntity;
        }).collect(Collectors.toList());

        modelResultProducer.pushLlmReturnResultData(MessageDTO.builder().source(clientId).data(modLlmReturnResultInfoRecordEntities).build());
        return modelList.size();
    }


    private List<ModLlmReturnResultInfoRecordEntity> mockList() {
//        String id = "82436c15b90f8712be7598423a3562fa";
        QueryWrapper<ModLlmReturnResultInfoRecordEntity> wrapper = new QueryWrapper<>();
        wrapper.in("new_id", Arrays.asList("e72e8b88a8a32a4a5139ac14bdfe4643"));
        wrapper.lambda().eq(ModLlmReturnResultInfoRecordEntity::getDone, "0");
        return this.list(wrapper);
    }

    @Override
    public Set<String> findUntreatedDataIds(int pushNormalizerFlowDatasetSize) {

        List<ModLlmReturnResultInfoRecordEntity> list = this.baseMapper.findUntreatedDataIds(pushNormalizerFlowDatasetSize);
//               List<ModLlmReturnResultInfoRecordEntity> list = this.mockList();
        if (CollUtil.isEmpty(list)) {
            return Collections.EMPTY_SET;
        }
        return list.stream().map(ModLlmReturnResultInfoRecordEntity::getNewId).collect(Collectors.toSet());
    }

    @Override
    public List<ModLlmReturnResultInfoRecordModel> findByIds(Set<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.EMPTY_LIST;
        }
        return this.findResultInfoRecordList(ids);
    }

    @Override
    public Set<String> findVoiceId(String originalId) {
        QueryWrapper<ModLlmReturnResultInfoRecordEntity> wrapper = new QueryWrapper<>();
        wrapper.select("new_id");
        wrapper.lambda().in(ModLlmReturnResultInfoRecordEntity::getOriginalId, originalId);
        wrapper.lambda().groupBy(ModLlmReturnResultInfoRecordEntity::getNewId);
        List<ModLlmReturnResultInfoRecordEntity> list = this.baseMapper.selectList(wrapper);

        return list.stream().map(ModLlmReturnResultInfoRecordEntity::getNewId).collect(Collectors.toSet());
    }

    private List<ModLlmReturnResultInfoRecordModel> findResultInfoRecordList(Set<String> newIdList) {
        List<ModLlmReturnResultInfoRecordEntity> rs = new ArrayList<>();
        List<List<String>> subList = CollUtil.split(newIdList, 200);
        subList.stream().forEach(sub -> {
            QueryWrapper<ModLlmReturnResultInfoRecordEntity> wrapper = new QueryWrapper<>();
            wrapper.in("new_id", sub);
            wrapper.eq("done", "0");
            rs.addAll(this.list(wrapper));
        });

        return modelConvertMapperService.cenvertToModLlmReturnResultInfoRecordEntityList(rs);
    }


    @Override
    public List<ModLlmReturnResultInfoRecordModel> findReturnResultInfoRecord(Set<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return null;
        }
        List<ModLlmReturnResultInfoRecordEntity> modLlmReturnResultInfoRecordEntities = this.list(new QueryWrapper<ModLlmReturnResultInfoRecordEntity>()
                .in("original_id", ids));
        return modelConvertMapperService.cenvertToModLlmReturnResultInfoRecordEntityList(modLlmReturnResultInfoRecordEntities);
    }

    @Override
    public int modifyToDoneDB(Set<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return 0;
        }
        int count = 0;
        List<List<String>> subList = CollUtil.split(ids, 200);
        for (List<String> subs : subList) {
            UpdateWrapper<ModLlmReturnResultInfoRecordEntity> wrapper = new UpdateWrapper<>();
            wrapper.in("new_id", subs);
            wrapper.set("done", "1");
            count += this.baseMapper.update(null, wrapper);
        }
        return count;
    }

    @Override
    public void modifyToDone(List<String> ids) {
        modelResultProducer.pushModifyData(MessageDTO.builder().data(ids).build());
    }


    @Override
    public Set<String> findByStatusDone(List<String> ids) {

        QueryWrapper<ModLlmReturnResultInfoRecordEntity> wrapper = new QueryWrapper<>();
        wrapper.in("new_id", ids);
        wrapper.lambda().eq(ModLlmReturnResultInfoRecordEntity::getDone, 1);
        List<ModLlmReturnResultInfoRecordEntity> list = this.list(wrapper);

        if (CollUtil.isNotEmpty(list)) {
            final Set<String> doneIds = list.stream().map(ModLlmReturnResultInfoRecordEntity::getNewId).collect(Collectors.toSet());
            return doneIds;
        }

        return Set.of();
    }
}





package com.voc.service.model.producers.kafka;

import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CatListener
 * @createTime 2024年03月14日 9:46
 * @Copyright cuick
 */
@Component("returnResultInfoRecord.producer.kafka")
public class ReturnResultInfoRecordProducer {
    public static final String TOPIC_DATA_RETRY = "VDP_modLlmReturnResultInfoRecord_retry";
    private static final Logger log = LoggerFactory.getLogger(ReturnResultInfoRecordProducer.class);
    @Autowired
    KafkaTemplate<String, String> kafkaTemplate;

    public void pushData(String message) throws Exception {
        log.info("推送数据 {}", message);
        Assert.isTrue(StrUtil.isNotEmpty(message), "message cannot be empty");
//        final String sendText = JSONUtil.toJsonStr(msg, JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
        kafkaTemplate.send(TOPIC_DATA_RETRY, message);
    }
}


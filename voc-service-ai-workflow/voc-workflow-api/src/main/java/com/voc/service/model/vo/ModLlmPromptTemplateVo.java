package com.voc.service.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 大模型prompt提示词模板表
 *
 * @TableName mod_llm_prompt_template
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModLlmPromptTemplateVo implements Serializable {
    /**
     * 主键
     */
    private String newId;


    /**
     * 请求类型：POST
     */
    private String method;

    /**
     * 请求地址
     */
    private String url;

    /**
     * 请求模型名称
     */
    private String model;

    /**
     * prompt
     */
    private String messages;

    private String source;


}
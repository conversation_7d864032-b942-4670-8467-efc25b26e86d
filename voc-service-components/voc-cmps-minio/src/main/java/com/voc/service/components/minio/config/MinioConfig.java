package com.voc.service.components.minio.config;

import com.voc.service.components.minio.service.UploadFileService;
import io.minio.MinioClient;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * MinIO配置
 *
 * <AUTHOR>
 */
@Configuration
@Data
public class MinioConfig {

    private static final Logger log = LoggerFactory.getLogger(MinioConfig.class);
    /**
     * endPoint是一个URL，域名，IPv4或者IPv6地址
     */
    @Value("${minio.oss.endpoint}")
    private String endpoint;

    /**
     * accessKey类似于用户ID，用于唯一标识你的账户
     */
    @Value("${minio.oss.accessKeyId}")
    private String accessKey;

    /**
     * secretKey是你账户的密码
     */
    @Value("${minio.oss.accessKeySecret}")
    private String secretKey;
    @Value("${minio.oss.web_endpoint}")
    private String webEndpoint;
    @Value("${minio.oss.bucketName}")
    private String bucketName;


    @Bean
    public MinioClient getMinioClient() {
        log.info("正在初始化MinIO...");
        MinioClient minioClient = MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .build();
        log.info("初始化MinIO成功!");
        return minioClient;
    }

    @Bean
    @Primary
    @ConditionalOnBean(MinioClient.class)
    public UploadFileService minioService() {

        return new UploadFileService();
    }
}

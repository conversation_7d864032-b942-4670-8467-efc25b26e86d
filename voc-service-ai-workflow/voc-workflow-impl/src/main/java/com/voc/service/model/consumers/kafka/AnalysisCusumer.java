package com.voc.service.model.consumers.kafka;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.voc.service.model.api.IModLlmBatchInfoRecordDetailedService;
import com.voc.service.model.enums.ModelTypeEnum;
import com.voc.service.model.model.AiRequestDataModel;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CatListener
 * @createTime 2024年03月14日 9:46
 * @Copyright cuick
 */
@Component("dataAnalysisCusumer.consumer.kafka")
public class AnalysisCusumer {
    private static final Logger log = LoggerFactory.getLogger(AnalysisCusumer.class);
    @Resource
    private FlowExecutor flowExecutor;
    // 使用线程安全的队列
    ConcurrentLinkedQueue<AiRequestDataModel> relist = new ConcurrentLinkedQueue<>();
    ConcurrentLinkedQueue<AiRequestDataModel> onlrelist = new ConcurrentLinkedQueue<>();
    int a = 0;
    @Value("${model.ai_workflow.llm.api.requestLineNumber:1000}")
    Integer requestLineNumber;
    @Value("${model.ai_workflow.llm.api.onlineRequestLineNumber}")
    Integer onlineRequestLineNumber;
    @Value("${model.ai_workflow.llm.api.noctreachModelNum}")
    Integer noctreachModelNum;
    @Value("${model.ai_workflow.llm.api.requestWaitTime:60000}")
    Integer requestWaitTime;
    @Value("${model.ai_workflow.llm.api.onlineRequestWaitTime:1}")
    Integer onlineRequestWaitTime;
    @Autowired
    IModLlmBatchInfoRecordDetailedService recordDetailedService;
    @Resource
    private KafkaListenerEndpointRegistry registry;
    Set<String> ids = new HashSet<>();
    AtomicLong offset = new AtomicLong();
    AtomicLong offsetPre = new AtomicLong();
    AtomicLong sentTime = new AtomicLong();

    AtomicInteger sign=new AtomicInteger();

    @PostConstruct
    void offsetListener(){
        AtomicLong startTime = new AtomicLong(System.currentTimeMillis());
        AtomicBoolean tet= new AtomicBoolean(false);
        new Thread(()->{
            while (true){
                if (relist.size()>0){
                    // 获取毫秒值并转换为分钟和秒
                    long totalMilliseconds = sentTime.get();
                    long minutes = totalMilliseconds / 60000;
                    long seconds = (totalMilliseconds % 60000) / 1000;
                    log.info("=====================离线模型时间控制=================：offset is {}, offsetPre is {}, sentTime is {}分钟{}秒", offset.get(), offsetPre.get(), minutes, seconds);
                    try {
                        if(offset.get()==offsetPre.get()&&offset.get()>0){
                            sentTime.addAndGet((System.currentTimeMillis() - startTime.get()) );
                            startTime.set(System.currentTimeMillis());
                        }else {
                            offsetPre.set(offset.get());
                            sentTime.set(0);
                        }
                        if(sentTime.get()>=(requestWaitTime*60000)){
                            registry.getListenerContainer("voc_llm_consumers").pause();
                            minutes = sentTime.get() / 60000;
                            seconds = (sentTime.get() % 60000) / 1000;
                            log.info("时长：{}分钟{}秒",minutes, seconds);
                            TimeUnit.SECONDS.sleep(10);
                            extracted();
                            registry.getListenerContainer("voc_llm_consumers").resume();
                            sentTime.set(0);
                        }
                        TimeUnit.SECONDS.sleep(60L);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }

                /*    log.info("offset is {},offsetPre is {}, sign is {}",offset.get(),offsetPre.get(),sign.get());
                    try {
                        if(offset.get()==offsetPre.get()&&offset.get()>0){
                            sign.incrementAndGet();
                        }else {
                            offsetPre.set(offset.get());
                            sign.set(0);
                        }
                        if(sign.get()>=onlineRequestWaitTime){
                            log.info("=====================实时模型时间控制=================：offset is {},offsetPre is {}, sign is {}",offset.get(),offsetPre.get(),sign.get());
                            registry.getListenerContainer("voc_llm_consumers").pause();
                            TimeUnit.SECONDS.sleep(60L);
                            extracted1();
                            registry.getListenerContainer("voc_llm_consumers").resume();
                        }
                        TimeUnit.SECONDS.sleep(60L);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }*/

            }
        }).start();
    }

    @KafkaListener(id ="voc_llm_consumers",topics ="${model.ai_workflow.llm.customer.topic.to_model_topic}", groupId = "${model.ai_workflow.llm.customer.group.to_model_topic_group}")
    public void onMessage(String message) {
        offset.incrementAndGet();
        AiRequestDataModel requestDataModel = null;
        try {
            requestDataModel = JSONUtil.toBean(message , AiRequestDataModel.class);
        } catch (Exception e) {
            log.error("AnalysisCusumer 消费解析失败：{}", message);
            e.printStackTrace();
            return;
        }
        log.info(">>>>>>>>>>>>>>>>> voc-ai-workflow-app 收到文章id {} 的请求 <<<<<<<<<<<<<<<<<<<<<<", requestDataModel.getId());

        if (requestDataModel.getModelType()==null){
            requestDataModel.setModelType("1");
        }else if (requestDataModel.getModelType().equals("null")||requestDataModel.getModelType().equals("")){
            requestDataModel.setModelType("1");
        }
        if (requestDataModel.getModelType().equals("2")) {// 实时模型
           /* onlrelist.add(requestDataModel);
            if (CollectionUtil.isNotEmpty(onlrelist)&&onlrelist.size()>=onlineRequestLineNumber) {
                registry.getListenerContainer("voc_llm_consumers").pause();
                extracted1();
            }*/
//            extracted(requestDataModel);

        }else if (requestDataModel.getModelType().equals("1")){//离线模型
            requestDataModel.setId(requestDataModel.getId());
            if (!ids.contains(requestDataModel.getId())){
                ids.add(requestDataModel.getId());
                relist.add(requestDataModel);
            }
            if (CollectionUtil.isNotEmpty(relist)&&relist.size()>=requestLineNumber) {
                registry.getListenerContainer("voc_llm_consumers").pause();
                ids.clear();
                extracted();
            }
        }

    }

    private synchronized void extracted1() {
        if(onlrelist.isEmpty()){
            return;
        }
        sign.set(0);
        List<AiRequestDataModel> list = new ArrayList<>(onlrelist);
        ExecutorService executorService = Executors.newFixedThreadPool(noctreachModelNum);; // 使用ForkJoinPool来提高效率
        for (AiRequestDataModel aiRequestDataModel : list) {
            executorService.submit(() -> {
                extracted(aiRequestDataModel);
            });
        }
        // 关闭线程池
        executorService.shutdown();
        try {
            // 等待所有任务完成
            executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }finally {
            onlrelist.clear();
        }
    }

    private void extracted(AiRequestDataModel requestDataModel) {
        ThirdPartyAIContext context = ThirdPartyAIContext.builder().build();
        context.setDataModel(requestDataModel);
        context.setModelType(ModelTypeEnum.AI_ONLINE.getType());
        try {
            context.setWorkId(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_FORMATTER));
            context.getStopWatch().start("执行 invoking_ai_model_flow");
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            log.info(">>>>>>>>>>>>>>>>> voc-ai-workflow-app 开始处理 {} 的请求 <<<<<<<<<<<<<<<<<<<<<<",requestDataModel.getId());
            // 执行原有逻辑
            LiteflowResponse response = flowExecutor.execute2Resp("invoking_ai_model_flow", context,context.getWorkId());
            if (!response.isSuccess()) {
                log.error("workId:{}  {}", context.getWorkId(), response.getCause());
                throw response.getCause();
            }

            // 计算并记录执行耗时
            long endTime = System.currentTimeMillis();
            long duration = (endTime - startTime) / 1000; // 转换为秒
            log.info("工作ID: {} 执行耗时: {} 秒", context.getWorkId(), duration);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private synchronized void extracted() {
        sentTime.set(0);
        if(relist.isEmpty()){
            return;
        }
        log.info(">>>>>>> 开始执行任务 <<<<<<<<<<<<<< {}，条数：{}", "数据组装任务", relist.size());
        ThirdPartyAIContext context = ThirdPartyAIContext.builder().build();
        context.setDataList(new ArrayList<>(relist)); // 转换为列表以供后续使用
        context.setModelType(ModelTypeEnum.AI_OFFLINE.getType());
        try {
            context.setWorkId(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_FORMATTER));
            context.getStopWatch().start("执行 invoking_ai_model_flow");
            relist.clear();
            LiteflowResponse response = flowExecutor.execute2Resp("invoking_ai_model_flow", context,context.getWorkId());
            relist.clear();
            if (!response.isSuccess()) {
                log.error("workId:{}  {}", context.getWorkId(), response.getCause());
                throw response.getCause();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            relist.clear();
        } finally {
//            context.getStopWatch().prettyPrint();
            relist.clear();
        }
        registry.getListenerContainer("voc_llm_consumers").resume();
    }


}


package com.voc.service.model.nodes;


import com.voc.service.model.api.IAnalysisService;
import com.voc.service.model.api.IModLlmReturnResultInfoRecordService;
import com.voc.service.model.config.AIWorkflowConfig;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryException;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "isExistAIResultUnprocessedDataSetNode", name = "查询Redis或DB是否有需要执行的数据ID集合节点")
public class IsExistAIResultUnprocessedDataSetNode extends AbstractNodeIf {

    @Autowired
    IAnalysisService iAnalysisService;
    @Autowired
    IModLlmReturnResultInfoRecordService iModLlmReturnResultInfoRecordService;
    @Autowired
    AIWorkflowConfig config;


    @Override
    public boolean processIf() throws Exception {
        try {
            /*//读取redis 未完成的数据ids
            final Set<String> redisIds = iAnalysisService.readAllUnprocessedIds();
            log.info("redisIds {}",redisIds.size());
            //读取db 未完成的数据ids
            final Set<String> dbIds = iModLlmReturnResultInfoRecordService.findUntreatedDataIds(config.getPushNormalizerFlowDatasetSize());
            log.info("dbIds {}",dbIds.size());
            //合并
            final Set<String> allIds = new HashSet<>(CollUtil.unionAll(redisIds, dbIds));
            log.info("allIds {}",allIds.size());
            if (CollUtil.isEmpty(allIds)) {
                log.info("未查询到第三方AI返回结果中未完成处理的数据集");
                return false;
            }
            if(CollUtil.isNotEmpty(allIds)) {
                //合并
                this.sendPrivateDeliveryData("loadAIResultUnprocessedDataNode", new HashSet<>(allIds));
            }*/
            return true;
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }

}

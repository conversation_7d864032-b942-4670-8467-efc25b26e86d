package com.voc.service.model.nodes;


import cn.hutool.core.util.ObjUtil;
import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryException;
import org.springframework.util.Assert;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "isExistUnprocessedDatasetDataNode", name = "过滤必填项字段数据节点")
public class IsExistUnprocessedDatasetDataNode extends AbstractNodeIf {
    @Autowired
    IMergeStatusRecordService mergeStatusRecordService;

    @Override
    public boolean processIf() throws Exception {
        try {
            AIResultDataContext context = this.getRequestData();

            this.sendPrivateDeliveryData("saveUnprocessedDatasetDataNode", this.getPrivateDeliveryData());
            return ObjUtil.isNotNull(this.getPrivateDeliveryData());
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }


    @Override
    public boolean isAccess() {
        Assert.isTrue(ObjUtil.isNotNull(this.getPrivateDeliveryData()), "getPrivateDeliveryData cannot be empty");
        return true;
    }

}

package com.voc.service.model.nodes;

import com.voc.service.model.api.IModLlmBatchInfoRecordService;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Title: Test
 * @Package: com.voc.service.model.nodes
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 15:04
 * @Version:1.0
 */
@LiteflowComponent(id = "loadAIModelBatchNumberNode", name = "加载本次需处理的AI模型批次号节点")
public class LoadAIModelBatchNumberNode extends AbstractNodeIf {

    private static final Logger log = LoggerFactory.getLogger(LoadAIModelBatchNumberNode.class);
    @Autowired
    IModLlmBatchInfoRecordService modLlmBatchInfoRecordService;


    @Override
    public boolean processIf() throws Exception {
        log.info("开始处理AI模型batch");
        ThirdPartyAIContext context = this.getRequestData();
        String batchId = context.getBatchId();
        Long count = modLlmBatchInfoRecordService.findRecordByBatchId(batchId);
        if (count < 0) {
            log.info("数据有误该batch 不存在或者已经处理完成");
            return Boolean.FALSE;
        }
        log.info("需要处理的AI模型batch:{}", batchId);
        return true;
    }
}

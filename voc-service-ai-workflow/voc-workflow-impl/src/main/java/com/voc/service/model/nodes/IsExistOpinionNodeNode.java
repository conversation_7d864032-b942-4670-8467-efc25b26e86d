package com.voc.service.model.nodes;


import cn.hutool.core.collection.CollUtil;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.retry.RetryException;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "isExistOpinionNodeNode", name = "检查是否有要处理的标签数据")
public class IsExistOpinionNodeNode extends AbstractNodeIf {

    @Override
    public boolean processIf() throws Exception {
        try {
            AIResultDataContext context = this.getRequestData();
            return CollUtil.isNotEmpty(context.getOpinionDataList());
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }

}

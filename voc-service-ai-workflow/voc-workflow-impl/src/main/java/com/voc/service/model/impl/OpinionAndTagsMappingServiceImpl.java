package com.voc.service.model.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.common.util.ServiceContextHolder;
import com.voc.service.components.mybatis.annotation.SwitchClientDS;
import com.voc.service.model.api.IOpinionAndTagsMappingService;
import com.voc.service.model.entity.OpinionAndTagsMappingEntity;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.OpinionAndTagsMappingMapper;
import com.voc.service.model.model.OpinionAndTagsMappingModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ConcurrentReferenceHashMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Title: CarAndDimChannelMappingServiceImpl
 * @Package: com.voc.service.model.impl
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/1 16:40
 * @Version:1.0
 */
@Service
public class OpinionAndTagsMappingServiceImpl extends ServiceImpl<OpinionAndTagsMappingMapper, OpinionAndTagsMappingEntity>
        implements IOpinionAndTagsMappingService {
    private static final Logger log = LoggerFactory.getLogger(OpinionAndTagsMappingServiceImpl.class);
    @Autowired
    ModelConvertMapperService convertMapperService;
    @CreateCache(area = "VDP", name = ":",  cacheType = CacheType.REMOTE)
    private Cache<String, Map<String, List<OpinionAndTagsMappingModel>>> clientsOpinionTagMappingCache;

    private static final String CLIENTS_OPINION_TAG_MAPPING_KEY = "{}:clients_opinion_tag_mapping:{}";

    private String getClientsOpinionTagMappingCacheKey(Object... params){
        return StrUtil.format(CLIENTS_OPINION_TAG_MAPPING_KEY, ServiceContextHolder.getSystemId(),params);
    }


    //    @SwitchClientDS
    @Override
    public Map<String, List<OpinionAndTagsMappingModel>> findAlMd5Maps(String clientId) {
        Assert.isTrue(StrUtil.isNotBlank(clientId), "clientId cannot be empty");
        Map<String, List<OpinionAndTagsMappingModel>> map = clientsOpinionTagMappingCache.get(this.getClientsOpinionTagMappingCacheKey(clientId));
        if (CollUtil.isEmpty(map)) {
            List<OpinionAndTagsMappingEntity> viewpointAndTagsMappingEntities = this.baseMapper.selectList(new QueryWrapper<>());
            List<OpinionAndTagsMappingModel> modelList = convertMapperService.cenvertToViewpointAndTagsMappingModelList(viewpointAndTagsMappingEntities);
            Map<String, List<OpinionAndTagsMappingModel>> maps = new ConcurrentReferenceHashMap<>();

            modelList.stream()
                    .map(model -> {
                        model.setMd5(this.getMd5(model.getOpinion()));
                        return model;
                    })
                    .forEach(model -> {
                        if (maps.containsKey(model.getMd5())) {
                            maps.get(model.getMd5()).add(model);
                        } else {
                            maps.put(model.getMd5(), new ArrayList<>(Arrays.asList(model)));
                        }
                    });
            map = maps;
            clientsOpinionTagMappingCache.put(this.getClientsOpinionTagMappingCacheKey(clientId), map);
        }

        return map;
    }

    @Override
    public String getMd5(String opinion) {
        return DigestUtil.md5Hex(opinion);
    }

    @Override
    @SwitchClientDS
    public List<OpinionAndTagsMappingModel> getModelList(String clientId, String opinion) {
        Map<String, List<OpinionAndTagsMappingModel>> maps = this.findAlMd5Maps(clientId);
        final String md5Str = this.getMd5(opinion);
        return maps.get(md5Str);
    }

    @Override
    public void cleanOpinionCache(String clientId) {
        Assert.isTrue(StrUtil.isNotBlank(clientId), "clientId cannot be empty");
        Map<String, List<OpinionAndTagsMappingModel>> map = clientsOpinionTagMappingCache.get(this.getClientsOpinionTagMappingCacheKey(clientId));
        if(CollUtil.isNotEmpty(map)){
            log.info("清除[clientId:{}}缓存",clientId);
            clientsOpinionTagMappingCache.remove(this.getClientsOpinionTagMappingCacheKey(clientId));
        }
    }

}

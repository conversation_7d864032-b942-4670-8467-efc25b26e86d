package com.voc.service.model.nodes;

import cn.hutool.core.util.StrUtil;
import com.voc.service.model.api.ICarAndDimChannelMappingService;
import com.voc.service.model.api.IScenariosTagsLevelMappingService;
import com.voc.service.model.config.AIWorkflowConfig;
import com.voc.service.model.model.ScenariosTagsLevelMappingModel;
import com.voc.service.model.model.VoiceClipRequestDataModel;
import com.voc.service.model.model.VoiceClipResultData;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.ModelContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "loadScenarioTagMappingDataNode", name = "接口获取标签映射数据（场景）节点")
public class LoadScenarioTagMappingDataNode extends AbstractNodeIf {
    private static final Logger log = LoggerFactory.getLogger(LoadScenarioTagMappingDataNode.class);
    @Autowired
    AIWorkflowConfig config;
    @Autowired
    ICarAndDimChannelMappingService carAndDimChannelMappingService;
    @Autowired
    IScenariosTagsLevelMappingService carAndDimOpinionMappingService;

    @Override
    public boolean processIf() throws Exception {
        try {
            ModelContext context = this.getRequestData();
            final String clientId = config.getTagMappingDb();
            List<VoiceClipResultData> processingLabeledDataset = context.getProcessingLabeledDataset();
            VoiceClipResultData voiceClipResultData = processingLabeledDataset.stream().findFirst().get();


            {
                final String tagsLevel4 = voiceClipResultData.getTagsLevel4();
                //获取scenario 结果数据集
                final List<ScenariosTagsLevelMappingModel> modelList = voiceClipResultData.getScenariosTagsLevelMappingList();
                if (ObjectUtils.isEmpty(modelList)) {
                    log.info("【标签第四级:{}】未匹配到对应的标签映射数据集", tagsLevel4);
                    final VoiceClipRequestDataModel inputDataset = context.getInputDataset();
                    context.setProcessingUnLabeledDataset(Arrays.asList(VoiceClipResultData.builder().
                            id(inputDataset.getNew_id())
                            .scenario_schema(inputDataset.getScenario())
                            .score(voiceClipResultData.getScore())
                            .contentId(inputDataset.getContentId())
                            .build()));
                    context.setProcessingLabeledDataset(Collections.EMPTY_LIST);
                    context.setNodeIdentifier("loadTagMappingData");
                    return false;
                } else {
                    log.info("获取scenario level 结果数据集");
                }
                //数据组装
                List<VoiceClipResultData> collect = modelList.stream().map(e -> {
                    final String scenarioSchema = StrUtil.join("->", e.getLevel1(), e.getLevel2(), e.getLevel3());
                    VoiceClipResultData build = VoiceClipResultData.builder().id(voiceClipResultData.getId()).scenario_schema(scenarioSchema).score(voiceClipResultData.getScore()).tagsLevel4(e.getLevel4()).build();
                    return build;
                }).collect(Collectors.toList());
                context.setProcessingLabeledDataset(collect);
                context.setProcessingUnLabeledDataset(Collections.EMPTY_LIST);
                log.info("scenario collect {}", collect.size());
            }

        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
        return true;
    }

    @Override
    public boolean isAccess() {
        ModelContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getTag()), "getTag cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(config.getTagMappingDb()), "clientId cannot be empty");
//        Assert.isTrue(ObjectUtils.isNotEmpty(context.getProcessingLabeledDataset()), "processingLabeledDataset cannot be empty");
//        Assert.isTrue(StrUtil.isNotBlank(context.getClientId()), "getClientId cannot be empty");
//        Assert.isTrue(CollUtil.isNotEmpty(context.getIds()), "getIds cannot be empty");


        return true;
    }

}

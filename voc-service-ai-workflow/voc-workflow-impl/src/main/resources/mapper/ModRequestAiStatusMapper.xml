<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voc.service.model.mapper.ModRequestAiStatusMapper">

    <select id="findStatusData" parameterType="com.voc.service.model.model.ModRequestAiStatusModel" >
        select voice_id ,sum(status) as number  from mod_request_ai_status where
        voice_id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by voice_id
    </select>

    <select id="findStatusList" parameterType="com.voc.service.model.model.ModRequestAiStatusModel" >
        select voice_id ,status as number from mod_request_ai_status where
        voice_id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by  voice_id,status
    </select>

</mapper>
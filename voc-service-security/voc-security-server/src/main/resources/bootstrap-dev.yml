spring.cloud.nacos.config.server-addr: ${NACOS_SERVER_ADDR_URLS:************:31600}
spring.cloud.nacos.config.username: nacos
spring.cloud.nacos.config.password: Passw0rd
spring.cloud.nacos.config.file-extension: yml
spring.cloud.nacos.config.group: ${NACOS_CONFIG_GROUP:DEFAULT_GROUP}
spring.cloud.nacos.config.namespace: ${NACOS_CONFIG_NAMESPACE:nissan-dndc-dev}
spring.cloud.nacos.config.extension-configs[0].data-id: common-nacos.yml
spring.cloud.nacos.config.extension-configs[1].data-id: common.yml
spring.cloud.nacos.config.extension-configs[2].data-id: common-sentinel.yml
spring.cloud.nacos.config.extension-configs[3].data-id: common-security-mysql.yml
spring.cloud.nacos.config.extension-configs[4].data-id: common-redis.yml
spring.cloud.nacos.config.extension-configs[6].data-id: common-swagger.yml
spring.cloud.nacos.config.extension-configs[5].data-id: voc-security-service.yml
spring.cloud.nacos.config.enabled: ${NACOS_CONFIG_ENABLED:true}
spring.cloud.nacos.discovery.enabled: ${NACOS_DISCOVERY_ENABLED:true}

com.alipay.sofa.boot.module-start-up-parallel: ${MODULE_START_UP_PARALLEL:true}
com.alipay.sofa.ark.master.biz: ${spring.application.name}
com.alipay.sofa.rpc.registry.address: nacos://${NACOS_SERVER_ADDR_URLS:************:30111}/${RPC_REGISTRY_NAMESPACE:sofa-rpc}



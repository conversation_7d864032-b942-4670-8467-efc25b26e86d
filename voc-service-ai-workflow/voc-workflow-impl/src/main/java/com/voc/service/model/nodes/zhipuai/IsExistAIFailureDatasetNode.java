package com.voc.service.model.nodes.zhipuai;


import cn.hutool.core.util.StrUtil;
import com.voc.service.model.api.IModLlmBatchInfoRecordService;
import com.voc.service.model.api.IModMetaDataAnalysisService;
import com.voc.service.model.mapper.ModMetaDataAnalysisMapper;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.voc.service.model.vo.ModLlmBatchInfoRecordVo;
import com.voc.service.trhird.api.ZhiPuAiApi;
import com.voc.service.trhird.model.ZhiPuStatusAiModel;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryException;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "isExistAIFailureDatasetNode", name = "检查是否有异常数据结果集节点")
public class IsExistAIFailureDatasetNode extends AbstractNodeIf {
    @Autowired
    IModLlmBatchInfoRecordService modLlmBatchInfoRecordService;
    @Autowired
    ZhiPuAiApi zhiPuAiApi;
    @Autowired
    IModMetaDataAnalysisService modMetaDataAnalysisService;
    @Autowired
    ModMetaDataAnalysisMapper modMetaDataAnalysisMapper;

    @Override
    public boolean processIf() throws Exception {
        try {
            ThirdPartyAIContext context = this.getRequestData();
            ZhiPuStatusAiModel model = context.getZhiPuStatusAiModel();
            ModLlmBatchInfoRecordVo batchInfoRecordVo = modLlmBatchInfoRecordService.getByBatchId(context.getBatchId());
            if (StrUtil.isNotBlank(model.getErrorFileId()) && batchInfoRecordVo.getErrorNum() == null) {
                return true;
            } else if (StrUtil.isNotBlank(model.getErrorFileId()) && batchInfoRecordVo.getErrorNum() != null && batchInfoRecordVo.getErrorNum() > 0) {//第二次失败
                //1.下载失败文件
                String tempPathErrorFile = zhiPuAiApi.downloadFilePathByFileId(context.getZhiPuStatusAiModel().getErrorFileId());
                List<String> customIds = new ArrayList<>();
                //2. 解析并提取失败的文章id （custom_id）
                extracted(tempPathErrorFile, customIds);
                //3.批量更新失败状态
                modMetaDataAnalysisService.batchUpdateStatus(customIds, 3);
                return false;
            } else {
                return false;
            }
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }

    private static void extracted(String tempPathErrorFile, List<String> customIds) {
        // 创建固定大小的线程池
        int numberOfThreads = Runtime.getRuntime().availableProcessors();
        ExecutorService executorService = Executors.newFixedThreadPool(numberOfThreads);

        try (BufferedReader reader = new BufferedReader(new FileReader(tempPathErrorFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 提交任务到线程池
                String finalLine = line;
                executorService.submit(() -> {
                    try {
                        JSONObject jsonObject = new JSONObject(finalLine);
                        String customId = jsonObject.getString("custom_id");
                        synchronized (customIds) {
                            customIds.add(customId);
                        }
                    } catch (JSONException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 关闭线程池并等待所有任务完成
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

}

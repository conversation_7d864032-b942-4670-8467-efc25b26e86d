package com.voc.service.model.web;

import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.common.response.Result;
import com.voc.service.model.api.IModLlmReturnResultInfoRecordService;
import com.voc.service.model.model.ModLlmReturnResultInfoRecordModel;
import com.voc.service.model.producers.kafka.UnprocessedResultDataProducer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/analysisCusumer")
public class KafkaConsumersController {
    private static final Logger log = LoggerFactory.getLogger(KafkaConsumersController.class);
    @Resource
    private KafkaListenerEndpointRegistry registry;
    @Autowired
    IModLlmReturnResultInfoRecordService modLlmReturnResultInfoRecordService;
    @Autowired
    UnprocessedResultDataProducer unprocessedResultDataProducer;

    @Operation(summary = "kafka consumer")
    @Parameter(name = "Authorization", in = ParameterIn.HEADER, required = true, description = "Bearer [token]")
    @GetMapping("/AnalysisCusumer")
    Result<?> AnalysisCusumer(String state) {
        try {
            if ("pause".equals(state)) {
                registry.getListenerContainer("voc_llm_consumers").pause();
            }
            if ("resume".equals(state)) {
                registry.getListenerContainer("voc_llm_consumers").resume();
            }
            return Result.OK();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("异常");
        }
    }

    @Operation(summary = "retry")
    @Parameter(name = "Authorization", in = ParameterIn.HEADER, required = true, description = "Bearer [token]")
    @GetMapping("/retry")
    Result<?> retry(String state) {
        try {
            Set<String> ids = modLlmReturnResultInfoRecordService.findUntreatedDataIds(100);
            List<ModLlmReturnResultInfoRecordModel> list = modLlmReturnResultInfoRecordService.findByIds(ids);
            log.info("执行数量：{}", list.size());
            list.stream().forEach(item -> {
                try {
                    unprocessedResultDataProducer.pushData(MessageDTO.builder().data(item).build());
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });

            return Result.OK();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("异常");
        }
    }
}

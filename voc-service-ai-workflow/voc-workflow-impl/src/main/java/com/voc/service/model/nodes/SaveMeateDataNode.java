package com.voc.service.model.nodes;

import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "saveMeateDataNode", name = "保存原始数据节点")
public class SaveMeateDataNode extends AbstractNode {

    @Override
    public void process() throws Exception {
        try {
            ThirdPartyAIContext context = this.getRequestData();

        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        ThirdPartyAIContext context = this.getRequestData();
//        Assert.isTrue(StrUtil.isNotBlank(context.getClientId()), "getClientId cannot be empty");
//        Assert.isTrue(CollUtil.isNotEmpty(context.getIds()), "getIds cannot be empty");


        return true;
    }

}

package com.voc.service.model.nodes;

import cn.hutool.core.util.StrUtil;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.util.Assert;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "saveBatchNumberNode", name = "保存调用批次数据节点")
public class SaveBatchNumberNode extends AbstractNode {

    @Override
    public void process() throws Exception {
        try {
            ThirdPartyAIContext context = this.getRequestData();

        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        ThirdPartyAIContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getBatchId()), "getClientId cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(context.getApiToken()), "getApiToken cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(context.getPrompt()), "getPrompt cannot be empty");

        return true;
    }

}

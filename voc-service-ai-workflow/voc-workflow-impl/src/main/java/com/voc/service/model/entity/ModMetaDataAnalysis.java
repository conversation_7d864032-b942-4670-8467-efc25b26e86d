package com.voc.service.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 大模型原文数据记录表
 *
 * @TableName mod_meta_data_analysis
 */
@TableName(value = "mod_meta_data_analysis")
@Data
public class ModMetaDataAnalysis implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "new_id", type = IdType.AUTO)
    private String newId;

    /**
     * 接收处理标识
     */
    private String workId;

    /**
     * 客户标识
     */
    private String clientId;

    /**
     * 渠道标识
     */
    private String channelId;

    /**
     * 内容类型：文本：text、 工单：order
     */
    private String contentType;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 是否完成计算 是：1，否：0
     */
    private Integer done;

    /**
     * 数据状态 0全部 1去噪数据 2已打标数据 3未打标数据
     */
    private Integer dataStatus;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 接收时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}

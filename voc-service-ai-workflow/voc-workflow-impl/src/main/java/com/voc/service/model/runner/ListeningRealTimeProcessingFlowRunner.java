package com.voc.service.model.runner;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.voc.service.model.entity.ModMetaDataAnalysisEntity;
import com.voc.service.model.enums.ModelTypeEnum;
import com.voc.service.model.mapper.ModMetaDataAnalysisMapper;
import com.voc.service.model.model.AiRequestDataModel;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Component
//@Order(value = 11)
public class ListeningRealTimeProcessingFlowRunner/* implements CommandLineRunner*/ {
    private static final Logger log = LoggerFactory.getLogger(ListeningRealTimeProcessingFlowRunner.class);
    @Resource
    private FlowExecutor flowExecutor;
    @Autowired
    Executor executor;
    final String chainId = "listening_realTime_processing_flowRunner";
    @Resource
    private RedissonClient redissonClient;
    RLock rlock;
    @Value("${model.ai_workflow.llm.api.onlineRequestLineNumber}")
    Integer onlineRequestLineNumber;
    @Value("${model.ai_workflow.llm.api.noctreachModelNum}")
    Integer noctreachModelNum;


    @Autowired
    ModMetaDataAnalysisMapper modMetaDataAnalysisMapper;
    @PostConstruct
    public void init() {
        rlock = redissonClient.getLock(chainId);
    }

    public ListeningRealTimeProcessingFlowRunner() {
        log.info("--->> {}", this.getClass().getSimpleName());
    }

    @Scheduled(cron = "0/5 * * * * ?")
  public void run() throws InterruptedException, ExecutionException, TimeoutException {
    try {
        if (!rlock.isLocked()) {
            rlock.lock();

            QueryWrapper<ModMetaDataAnalysisEntity> wrapper = new QueryWrapper<>();
            wrapper.lambda()
                   .orderByDesc(ModMetaDataAnalysisEntity::getCreateTime)
                   .isNull(ModMetaDataAnalysisEntity::getDone)
                   .eq(ModMetaDataAnalysisEntity::getModelType, 2)
                   .last("LIMIT " + onlineRequestLineNumber);

            List<ModMetaDataAnalysisEntity> list = modMetaDataAnalysisMapper.selectList(wrapper);
            log.info("实时处理定时任务开始处理读取：{}条数据",list.size());
            if (CollectionUtil.isNotEmpty(list)) {
                UpdateWrapper<ModMetaDataAnalysisEntity> update = new UpdateWrapper<>();
                update.lambda()
                      .set(ModMetaDataAnalysisEntity::getDone, 2)
                      .in(ModMetaDataAnalysisEntity::getNewId,
                          list.stream().map(ModMetaDataAnalysisEntity::getNewId).collect(Collectors.toSet()));

                int updatedCount = modMetaDataAnalysisMapper.update(null, update);

                if (updatedCount > 0) {
                    // 使用更合适的线程池管理策略
                    ExecutorService executorService = Executors.newFixedThreadPool(noctreachModelNum);
                    List<Future<?>> futures = new ArrayList<>();
                    for (ModMetaDataAnalysisEntity entity : list) {
                        Future<?> future = executorService.submit(() -> {
                            AiRequestDataModel aiRequestDataModel = AiRequestDataModel.builder()
                                    .id(entity.getNewId())
                                    .content(entity.getContent())
                                    .modelType(entity.getModelType())
                                    .source(entity.getContentType())
                                    .workId(entity.getWorkId())
                                    .clientId(entity.getClientId())
                                    .build();
                            extracted(aiRequestDataModel);
                        });
                        futures.add(future);
                    }
                    // 等待所有任务完成
                    for (Future<?> future : futures) {
                        future.get(); // 这里可以设置超时时间，例如 future.get(30, TimeUnit.SECONDS);
                    }
                    // 关闭线程池
                    executorService.shutdown();
                }
            }
        }
    } catch (Exception e) {
        log.error("实时处理定时任务报错：{},{}",e.getMessage());
        e.printStackTrace();
    } finally {
        if (rlock.isHeldByCurrentThread()) {
            rlock.unlock();
        }
        log.info("chainId {} 完成", chainId);
    }
}

    private void extracted(AiRequestDataModel requestDataModel) {
        ThirdPartyAIContext context = ThirdPartyAIContext.builder().build();
        context.setDataModel(requestDataModel);
        context.setModelType(ModelTypeEnum.AI_ONLINE.getType());
        try {
            context.setWorkId(requestDataModel.getId());
            context.getStopWatch().start("执行 invoking_ai_model_flow");
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            log.info(">>>>>>>>>>>>>>>>> voc-ai-workflow-app 开始处理 {} 的请求 <<<<<<<<<<<<<<<<<<<<<<",requestDataModel.getId());
            // 执行原有逻辑
            LiteflowResponse response = flowExecutor.execute2Resp("invoking_ai_model_flow", context,context.getWorkId());
            if (!response.isSuccess()) {
                log.error("workId:{}  {}", context.getWorkId(), response.getCause());
                throw response.getCause();
            }

            // 计算并记录执行耗时
            long endTime = System.currentTimeMillis();
            long duration = (endTime - startTime) / 1000; // 转换为秒
            log.info("工作ID: {} 执行耗时: {} 秒", context.getWorkId(), duration);

        } catch (Exception e) {
            e.printStackTrace();
            log.error("实时处理work流报错:{}",e.getMessage());
        }
    }



}

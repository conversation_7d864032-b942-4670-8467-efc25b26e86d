package com.voc.service.model.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voc.service.model.entity.ModLlmReturnResultInfoRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;


@Mapper
@Repository
public interface ModLlmReturnResultInfoRecordMapper extends BaseMapper<ModLlmReturnResultInfoRecordEntity> {

//    List<ModLlmReturnResultInfoRecordEntity> findUntreatedData(int pushNormalizerFlowDatasetSize);

    List<ModLlmReturnResultInfoRecordEntity> findUntreatedDataIds(int pushNormalizerFlowDatasetSize);
}

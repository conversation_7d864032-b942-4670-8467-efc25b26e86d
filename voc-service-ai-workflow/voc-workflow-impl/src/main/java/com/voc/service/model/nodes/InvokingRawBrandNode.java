package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.voc.service.common.util.ServiceContextHolder;
import com.voc.service.model.api.IModMetaDataAnalysisService;
import com.voc.service.model.clients.IModelBrandCarServiceClient;
import com.voc.service.model.config.AIWorkflowConfig;
import com.voc.service.model.model.AiBrandAndCarModel;
import com.voc.service.model.model.LabelModel;
import com.voc.service.model.model.ModLlmReturnResultInfoRecordModel;
import com.voc.service.model.model.ModMetaDataAnalysisModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.voc.service.model.vo.BrandCarVo;
import com.voc.service.model.vo.ModBrandCarVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "invokingRawBrandNode", name = "调用模型品牌分析节点")
public class InvokingRawBrandNode extends AbstractNode {


    private static final Logger log = LoggerFactory.getLogger(InvokingRawBrandNode.class);
    @Autowired
    IModelBrandCarServiceClient iModelBrandCarServiceClient;

    @Autowired
    IModMetaDataAnalysisService iModMetaDataAnalysisService;

    @Autowired
    AIWorkflowConfig config;

    @Override
    public void process() throws Exception {
        try {
            AIResultDataContext context = this.getRequestData();
            List<ModLlmReturnResultInfoRecordModel> untreatedDataList = context.getBrandCarDataList();
            if (CollUtil.isEmpty(untreatedDataList)) {
                log.debug("无数据，跳过本次节点");
                return;
            }
            final List<String> customIdList = untreatedDataList.stream().map(ModLlmReturnResultInfoRecordModel::getOriginalId).toList();
            List<ModMetaDataAnalysisModel> modMetaDataAnalysisList = iModMetaDataAnalysisService.findModMetaDataAnalysisList(customIdList);

            //组装请求模型品牌车系数据
            List<AiBrandAndCarModel> aiBrandAndCarModelList = new ArrayList<>();
            for (ModLlmReturnResultInfoRecordModel model : untreatedDataList) {
                this.makeBrandCarData(model.getOriginalId(), model.getNewId(), model, modMetaDataAnalysisList, aiBrandAndCarModelList);
            }
            if (CollectionUtil.isEmpty(aiBrandAndCarModelList)) {
                log.debug("无数据，跳过本次节点");
                return;
            }
            try {
                //设置固定token
                ServiceContextHolder.setToken(config.getDefaultToken());
                log.info("调用品牌车系执行声音ID:{}", aiBrandAndCarModelList.get(0).getId());
                log.info("调用品牌车系入参:{}", aiBrandAndCarModelList.size());
                ModBrandCarVo modBrandCarVo = iModelBrandCarServiceClient.vehicleAnalysis(aiBrandAndCarModelList);
                log.info("调用品牌车系返回:{}", modBrandCarVo);
                if (modBrandCarVo.getSuccess() && CollectionUtil.isNotEmpty(modBrandCarVo.getResult())) {
                    List<BrandCarVo> result = modBrandCarVo.getResult();
                    context.setBrandCarVoList(result);
                } else {
                    log.error("调用模型品牌车系接口错误:{}", modBrandCarVo);
                    throw new Exception("调用模型品牌车系接口错误");
                }
            } catch (Exception e) {
                log.error("调用模型品牌车系接口错误:", e);
            }
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }


    /**
     * 组装请求模型品牌车系数据
     *
     * @param customId
     * @param voiceId
     * @param modMetaDataAnalysisList
     * @param aiBrandAndCarModelList
     */
    private void makeBrandCarData(String customId, String voiceId,
                                  ModLlmReturnResultInfoRecordModel model,
                                  List<ModMetaDataAnalysisModel> modMetaDataAnalysisList,
                                  List<AiBrandAndCarModel> aiBrandAndCarModelList) {
        Map<String, AiBrandAndCarModel> brandAndCarModelMap = aiBrandAndCarModelList.stream().collect(Collectors.toMap(AiBrandAndCarModel::getId, Function.identity()));
        if (brandAndCarModelMap.containsKey(customId)) {
            AiBrandAndCarModel aiBrandAndCarModel = brandAndCarModelMap.get(customId);
            List<LabelModel> result = aiBrandAndCarModel.getResult();
            LabelModel brandCarModel = new LabelModel();
            brandCarModel.setVehicleModel(model.getVehicleModel());
            brandCarModel.setVehicleBrand(model.getVehicleBrand());
            brandCarModel.setVoiceId(voiceId);
            brandCarModel.setSubject(model.getSubject());
            brandCarModel.setAspect(model.getAspect());
            brandCarModel.setDesc(model.getDescription());
            result.add(brandCarModel);
        } else {
            AiBrandAndCarModel aiBrandAndCarModel = new AiBrandAndCarModel();
            aiBrandAndCarModel.setId(customId);
            if (CollectionUtil.isNotEmpty(modMetaDataAnalysisList)) {
                Map<String, ModMetaDataAnalysisModel> metaDataAnalysisModelMap = modMetaDataAnalysisList.stream().collect(Collectors.toMap(ModMetaDataAnalysisModel::getNewId, Function.identity()));
                if (metaDataAnalysisModelMap.containsKey(customId)) {
                    aiBrandAndCarModel.setContent(metaDataAnalysisModelMap.get(customId).getContent());
                }
            }
            List<LabelModel> resultList = new ArrayList<>();
            LabelModel brandCarModel = new LabelModel();
            brandCarModel.setVehicleModel(model.getVehicleModel());
            brandCarModel.setVehicleBrand(model.getVehicleBrand());
            brandCarModel.setVoiceId(voiceId);
            brandCarModel.setSubject(model.getSubject());
            brandCarModel.setAspect(model.getAspect());
            brandCarModel.setDesc(model.getDescription());
            resultList.add(brandCarModel);
            aiBrandAndCarModel.setResult(resultList);
            aiBrandAndCarModelList.add(aiBrandAndCarModel);
        }
    }

    @Override
    public boolean isAccess() {
        return true;
    }

}

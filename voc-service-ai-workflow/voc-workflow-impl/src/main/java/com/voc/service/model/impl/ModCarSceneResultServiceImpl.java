package com.voc.service.model.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.model.api.IModCarSceneResultService;
import com.voc.service.model.entity.ModCarSceneResultEntity;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.ModCarSceneResultMapper;
import com.voc.service.model.model.ModCarSceneResultModel;
import com.voc.service.model.producers.kafka.ModelResultProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * @Title: ModCarSceneResultServiceImpl
 * @Package: com.voc.service.model.impl
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/6 10:23
 * @Version:1.0
 */
@Service
public class ModCarSceneResultServiceImpl extends ServiceImpl<ModCarSceneResultMapper, ModCarSceneResultEntity>
        implements IModCarSceneResultService {
    @Autowired
    ModelConvertMapperService convertMapperService;

    @Autowired
    ModelResultProducer modelResultProducer;

    @Override
    public boolean add(String clientId, List<ModCarSceneResultModel> list) {
        if (CollUtil.isEmpty(list)) {
            return false;
        }

        List<ModCarSceneResultEntity> entity = convertMapperService.cenvertToModCarSceneResultEntityList(list);
        modelResultProducer.pushCarSceneResultData(MessageDTO.builder().source(clientId).data(entity).build());
        return Boolean.TRUE;
    }

    @Override
    public List<ModCarSceneResultModel> findModCarSceneResult(Set<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return null;
        }
        List<ModCarSceneResultEntity> modCarSceneResultEntities = this.list(new QueryWrapper<ModCarSceneResultEntity>()
                .in("original_id", ids));
        return convertMapperService.cenvertToModCarSceneResultModelList(modCarSceneResultEntities);
    }
}

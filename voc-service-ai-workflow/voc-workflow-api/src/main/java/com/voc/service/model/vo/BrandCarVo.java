package com.voc.service.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrandCarVo implements Serializable {

    private String id;

    private List<BrandCar> result;

    @Data
    public static class BrandCar {

        private String voiceId;

        private String carSeries;

        private String carSeriesCode;

        private String brand;

        private String brandCode;
    }
}
package com.voc.service.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Title: MergingResultDataEntity
 * @Package: com.voc.service.model.entity
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 16:38
 * @Version:1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "mod_ftm_scenarios_not_tag_result")
public class ModScenariosNotTagResultEntity implements Serializable {

    private String newId; // 主键
    private String voiceId; // 声音ID
    private String originalId; // 原文ID
    private String workId; // 接收处理标识
    private String opinion; // 观点
    private String opinionSentiment; // 观点情感
    private String subject; // 主体
    private String description; // 描述
    private String carBodyLabel; // 整车体系
    private String viewLabel; // 评价维度
    private LocalDateTime createTime; // 接收时间
}

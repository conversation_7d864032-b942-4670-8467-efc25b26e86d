package com.voc.service.model.consumers.kafka;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.model.nodes.abstracts.AbstractContext;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.voc.service.model.producers.kafka.EventProducer;
import com.voc.service.model.producers.kafka.ModelResultProducer;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CatListener
 * @createTime 2024年03月14日 9:46
 * @Copyright cuick
 */
@Component("eventCusumer.consumer.kafka")
public class EventCusumer {
    private static final Logger log = LoggerFactory.getLogger(EventCusumer.class);
    @Resource
    private FlowExecutor flowExecutor;
    @Autowired
    ModelResultProducer vocModelProducer;

    @KafkaListener(topics = {EventProducer.TOPIC_EVENT}, groupId = "${model.ai_workflow.llm.customer.group.to_model_topic_group}")
    public void onMessage(@Payload String message) {
        log.debug(">>>>>>> 收到 {} 的请求 <<<<<<<<<<<<<<", message);
        AbstractContext context = new AbstractContext();
        MessageDTO dto = null;
        try {
            dto = JSONUtil.toBean(message, MessageDTO.class);
            if (ObjUtil.isNull(dto)) {
                log.error("dto {}", dto);
                return;
            }
            Assert.isTrue(StrUtil.isNotBlank(dto.getType()), "getType cannot be empty");
            log.info(">>>>>>> 开始执行任务 <<<<<<<<<<<<<< {}", dto.getType());

            if ("invokingAIModel".equalsIgnoreCase(dto.getType())) {
                final String chainId = "invoking_ai_model_flow";
                ThirdPartyAIContext aiContext = ThirdPartyAIContext.builder().build();
                aiContext.setWorkId(dto.getRequestId());
                aiContext.getStopWatch().start("执行 ".concat(chainId));
                LiteflowResponse response = flowExecutor.execute2Resp(chainId, aiContext,context.getWorkId());
                if (!response.isSuccess()) {
                    log.error("workId:{}  {}", aiContext.getWorkId(), response.getCause());
                    throw response.getCause();
                }
                context = aiContext;
            } else {
                log.error("无效的事件定义 [{}]", dto.getType());
            }

        } catch (Exception e) {
//            ack.nack(1000);
            log.error(e.getMessage(), e);
        } finally {
            log.info("workId {} 完成 ", context.getWorkId());
//            context.getStopWatch().prettyPrint();
        }
    }

}


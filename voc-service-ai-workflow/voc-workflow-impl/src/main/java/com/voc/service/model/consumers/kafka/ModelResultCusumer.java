package com.voc.service.model.consumers.kafka;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.analysis.dto.MessageExt;
import com.voc.service.model.api.IAnalysisService;
import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.api.IModLlmReturnResultInfoRecordService;
import com.voc.service.model.nodes.abstracts.AbstractContext;
import com.voc.service.model.producers.kafka.ModelResultProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CatListener
 * @createTime 2024年03月14日 9:46
 * @Copyright cuick
 * @Description 再推送数据清洗服务前的【数据结果表】
 */
@Component("modelResult.cusumer.kafka")
public class ModelResultCusumer {
    private static final Logger log = LoggerFactory.getLogger(ModelResultCusumer.class);
    @Autowired
    IModLlmReturnResultInfoRecordService modLlmReturnResultInfoRecordService;
    @Autowired
    IMergeStatusRecordService mergeStatusRecordService;
    @Autowired
    IAnalysisService analysisService;
    @Autowired
    ModelResultProducer modelResultProducer;

    @KafkaListener(topics = {ModelResultProducer.TOPIC_LLM_RETURN_RESULT_MODIFY}, groupId = "${model.ai_workflow.llm.customer.group.to_model_topic_group}")
    public void onMessage(@Payload String message) {
        log.debug(">>>>>>> 收到 {} 的请求 <<<<<<<<<<<<<<", message);
        AbstractContext context = new AbstractContext();
        MessageDTO dto = null;
        try {
            dto = JSONUtil.toBean(message, MessageDTO.class);
            if (ObjUtil.isNull(dto)) {
                log.error("dto {}", dto);
                return;
            }
            log.info(">>>>>>> 开始执行任务 <<<<<<<<<<<<<< {}", dto.getType());
            final List<String> ids = JSONUtil.toList(JSONUtil.parseArray(dto.getData()), String.class);
            modLlmReturnResultInfoRecordService.modifyToDoneDB(new HashSet<>(ids));

        } catch (Exception e) {
//            ack.nack(1000);
            log.error(e.getMessage(), e);
        } finally {
            log.info("workId {} 完成 ", context.getWorkId());
//            context.getStopWatch().prettyPrint();
        }
    }

    @KafkaListener(topics = {ModelResultProducer.TOPIC_LLM_RETURN_RESULT_MODIFY_EVENT}, groupId = "${model.ai_workflow.llm.customer.group.to_model_topic_group}")
    public void onMessage2(@Payload String message) {
        log.debug(">>>>>>> 收到 {} 的请求 <<<<<<<<<<<<<<", message);
        AbstractContext context = new AbstractContext();
        MessageDTO dto = null;
        try {
            dto = JSONUtil.toBean(message, MessageDTO.class);
//            if (true) {
            if (ObjUtil.isNull(dto)) {
//                log.error("dto {}", dto);
                return;
            }

            final Optional<MessageExt> retry = dto.getExt().stream().filter(att -> att.getKey().equals("retry")).findAny();
            if (retry.isPresent()) {
                if (ObjectUtil.isNotNull(retry.get().getValue()) && (int) retry.get().getValue() >= 60) {
                    log.error(">>>>>>> 重试次数过多，放弃重试 <<<<<<<<<<<<<< {}", dto.getData());

                    return;
                }
            }

            log.info(">>>>>>> 开始执行任务 <<<<<<<<<<<<<< {}", dto.getType());
            final List<String> ids = JSONUtil.toList(JSONUtil.parseArray(dto.getData()), String.class);
            //查询数据状态- 已完成的数据集合
            final Set<String> doneIds = modLlmReturnResultInfoRecordService.findByStatusDone(ids);

            log.info("查询数据状态- 已完成的数据集合 {}", doneIds);
            //交集
            final Collection<String> updateIds = CollUtil.intersection(ids, doneIds);

            if (CollUtil.isNotEmpty(updateIds)) {
                log.info("删除redis key 集合 {}", updateIds.size());
//                log.error("updateIds {}",updateIds.size());
                //修改成完成状态
//                mergeStatusRecordService.modifyToStatusDB(new HashSet<>(updateIds));
                //删除redis key
//                analysisService.removeDataStatus(new HashSet<>(updateIds));
            }
            //未完成状态修改的ids集合 -- 差集
            final Collection<String> notFindIds = CollUtil.subtract(ids, doneIds);
            if (CollUtil.isNotEmpty(notFindIds)) {
                log.info("未完成状态修改的ids集合 {}", notFindIds);
                //重新放入事件通知
                try {
                    Thread.sleep(1000);
                    MessageDTO retryDto = MessageDTO.builder().data(dto.getData()).ext(dto.getExt()).build();
                    MessageExt retryExt = dto.getExt().stream().filter(att -> att.getKey().equals("retry")).findAny()
                            .orElse(MessageExt.builder().key("retry").value(0).build());
                    retryExt.setValue((int) retryExt.getValue() + 1);
                    retryDto.getExt().add(retryExt);

                    //   modelResultProducer.pushModifyEvent(retryDto);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }

        } catch (Exception e) {
//            ack.nack(1000);
            log.error(e.getMessage(), e);
        } finally {
//            log.info("workId {} 完成 ", context.getWorkId());
//            context.getStopWatch().prettyPrint();
        }
    }
}


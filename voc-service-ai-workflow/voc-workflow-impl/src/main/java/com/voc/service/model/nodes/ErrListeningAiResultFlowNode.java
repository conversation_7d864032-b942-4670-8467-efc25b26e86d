package com.voc.service.model.nodes;

import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.yomahub.liteflow.annotation.LiteflowComponent;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "errListeningAiResultFlowNode", name = "监听第三方服务返回结果异常节点")
public class ErrListeningAiResultFlowNode extends AbstractNode {

    @Override
    public void process() throws Exception {

    }
}

package com.voc.service.model.listener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * @Title: KafkaListener
 * @Package: com.voc.service.model.listener
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/22 14:48
 * @Version:1.0
 */
@Component
public class KafkaListener {
    private static final Logger log = LoggerFactory.getLogger(KafkaListener.class);
    @Autowired
    private KafkaListenerEndpointRegistry registry;


    public void stopListener(Set<String> topics) {
        Optional.ofNullable(topics).orElse(new HashSet<>()).forEach(topic -> {
            MessageListenerContainer messageListenerContainer = registry
                    .getListenerContainer(topic);
            if (Objects.nonNull(messageListenerContainer) && !messageListenerContainer.isContainerPaused()) {
                log.info("TOPIC {} 已暂停", topic);
                messageListenerContainer.pause();
            }
        });
    }

    public void startListener(Set<String> topics) {
        Optional.ofNullable(topics).orElse(new HashSet<>()).forEach(topic -> {
            MessageListenerContainer messageListenerContainer = registry
                    .getListenerContainer(topic);
            if (Objects.nonNull(messageListenerContainer) && messageListenerContainer.isContainerPaused()) {
                log.info("TOPIC {} 已恢复", topic);
                messageListenerContainer.resume();
            }
        });
    }

    public boolean isListenerRunning(String topic) {
        MessageListenerContainer messageListenerContainer = registry
                .getListenerContainer(topic);
        if (Objects.nonNull(messageListenerContainer) && !messageListenerContainer.isContainerPaused()) {
            return true;
        }
        return false;
    }
}

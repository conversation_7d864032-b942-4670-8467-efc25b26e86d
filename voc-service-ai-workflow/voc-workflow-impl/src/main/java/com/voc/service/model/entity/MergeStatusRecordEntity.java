package com.voc.service.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Title: MergingResultDataEntity
 * @Package: com.voc.service.model.entity
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 16:38
 * @Version:1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "mod_merge_status_record")
public class MergeStatusRecordEntity implements Serializable {
    String newId;
    String workId;
    String voiceId;
    String originalId;
    String clientId;
    int labelNum;
    int scenarioNum;
    int brandCarNum;
    @Builder.Default
    LocalDateTime createTime = LocalDateTime.now();
    @Builder.Default
    LocalDateTime updateTime = LocalDateTime.now();
    @Builder.Default
    int status = 0;
    @Builder.Default
    int presetStatus = 14;
    @Builder.Default
    int done = 0;
    private String channelId;
    private String contentType;
    private Integer modelType;
    private Object extFields;
}

package com.voc.service.model.nodes.context;

import com.voc.service.model.nodes.abstracts.AbstractContext;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * @Title: AiWorkflowDefaultContext
 * @Package: com.voc.service.model.nodes.context
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 14:28
 * @Version:1.0
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
public class AnalysisDataContext extends AbstractContext implements Serializable {

    @Builder.Default
    Set<String> ids = new HashSet<>();
    @Schema(description = "客户ID")
    String clientId;
}

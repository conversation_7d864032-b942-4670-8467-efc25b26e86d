package com.voc.service.model.nodes;


import cn.hutool.core.collection.CollUtil;
import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.model.ModLlmReturnResultInfoRecordModel;
import com.voc.service.model.model.ModWorkflowModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryException;
import org.springframework.util.Assert;

import java.util.*;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "saveUnprocessedDatasetDataNode", name = "过滤数据处理逻辑节点")
public class SaveUnprocessedDatasetDataNode extends AbstractNode {
    private static final Logger log = LoggerFactory.getLogger(SaveUnprocessedDatasetDataNode.class);
    @Autowired
    IMergeStatusRecordService mergeStatusRecordService;

    @Override
    public void process() throws Exception {

        AIResultDataContext context = this.getRequestData();
        Map<String, List<ModLlmReturnResultInfoRecordModel>> map = context.getFilteredRequiredFieldsMap();
        if (CollUtil.isEmpty(map)) {
            log.info("过滤数据处理逻辑节点map是空");
            return;
        }
        log.info("开始处理过滤数据的处理");
        Map<Integer, List<ModWorkflowModel>> sendMap = new HashMap<>();
        try {
            //过滤掉NA类型数，并修改成0， 不执行状态
            for (String tag : map.keySet()) {
                final List<ModLlmReturnResultInfoRecordModel> list = map.get(tag);
                log.info("{} 处理数据大小：{}", tag, list.size());
                if ("brand".equalsIgnoreCase(tag)) {
                    for (ModLlmReturnResultInfoRecordModel model : list) {

                        final ModWorkflowModel priModel = ModWorkflowModel.builder().voiceId(model.getNewId()).originalId(model.getOriginalId()).build();
                        if (sendMap.containsKey(2)) {
                            sendMap.get(2).add(priModel);
                        } else {
                            sendMap.put(2, new ArrayList<>(Collections.singletonList(priModel)));
                        }
                    }

                } else if ("opinion".equalsIgnoreCase(tag)) {
                    for (ModLlmReturnResultInfoRecordModel model : list) {
                        final ModWorkflowModel priModel = ModWorkflowModel.builder().voiceId(model.getNewId()).originalId(model.getOriginalId()).build();
                        if (sendMap.containsKey(4)) {
                            sendMap.get(4).add(priModel);
                        } else {
                            sendMap.put(4, new ArrayList<>(Collections.singletonList(priModel)));
                        }
                    }

                } else if ("scenario".equalsIgnoreCase(tag)) {
                    for (ModLlmReturnResultInfoRecordModel model : list) {
                        final ModWorkflowModel priModel = ModWorkflowModel.builder().voiceId(model.getNewId()).originalId(model.getOriginalId()).build();
                        if (sendMap.containsKey(8)) {
                            sendMap.get(8).add(priModel);
                        } else {
                            sendMap.put(8, new ArrayList<>(Collections.singletonList(priModel)));
                        }
                    }

                } else ;
            }

        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }finally {
            log.info("saveUnprocessedDatasetDataNode过滤数据集合:{}", sendMap.size());
            if (CollUtil.isNotEmpty(sendMap)) {
                context.setFilterMap(sendMap);
            }
        }
    }

    @Override
    public boolean isAccess() {
        AIResultDataContext context = this.getRequestData();
        Assert.isTrue(CollUtil.isNotEmpty(context.getFilteredRequiredFieldsMap()), "getFilteredRequiredFieldsMap cannot be empty");
        return true;
    }

}

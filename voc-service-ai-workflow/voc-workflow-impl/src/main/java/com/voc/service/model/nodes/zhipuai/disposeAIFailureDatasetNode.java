package com.voc.service.model.nodes.zhipuai;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import com.voc.service.common.util.ServiceContextHolder;
import com.voc.service.components.minio.service.UploadFileService;
import com.voc.service.model.api.IModLlmBatchInfoRecordDetailedService;
import com.voc.service.model.entity.ModLlmBatchInfoRecord;
import com.voc.service.model.enums.BatchStatusEnum;
import com.voc.service.model.mapper.ModLlmBatchInfoRecordMapper;
import com.voc.service.model.model.ModLlmBatchInfoRecordDetailedModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.voc.service.model.producers.kafka.AnalysisProducer;
import com.voc.service.trhird.api.Zhi<PERSON>uAiApi;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.zhipu.oapi.service.v4.batchs.BatchResponse;
import com.zhipu.oapi.service.v4.file.FileApiResponse;
import com.zhipu.oapi.service.v4.file.UploadFileRequest;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "disposeAIFailureDatasetNode", name = "处理AI返回失败数据节点")
public class disposeAIFailureDatasetNode extends AbstractNode {
    private static final Logger log = LoggerFactory.getLogger(disposeAIFailureDatasetNode.class);
    @Autowired
    ZhiPuAiApi zhiPuAiApi;
    @Autowired
    UploadFileService uploadFileService;
    @Autowired
    ModLlmBatchInfoRecordMapper batchInfoRecordMapper;
    @Autowired
    IModLlmBatchInfoRecordDetailedService batchInfoRecordDetailedService;
    @Autowired
    AnalysisProducer analysisProducer;

    @Override
    public void process() throws Exception {
        try {
            ThirdPartyAIContext context = this.getRequestData();
            //1.下载失败文件
            String tempPathErrorFile = zhiPuAiApi.downloadFilePathByFileId(context.getZhiPuStatusAiModel().getErrorFileId());
            List<String> customIds = new ArrayList<>();
            //2. 解析并提取失败的文章id （custom_id）
            extracted(tempPathErrorFile, customIds);
            //3. 下载原始的batch文件数据 && 删除成功的数据
            String tempPathBatchFile = zhiPuAiApi.downloadFilePathByFileId(context.getZhiPuStatusAiModel().getInputFileId());
            String fileName = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_FORMATTER) + RandomUtil.randomNumbers(3);
            Path tempPath = Files.createTempFile(fileName, ".jsonl");
            String resourcePath = tempPath.toString();
            Integer batchSize = extracted(tempPathBatchFile, resourcePath, customIds);
            extracted("batch", Paths.get(resourcePath), customIds, context.getBatchId());
            //4. 删除临时文件
            Files.delete(Paths.get(tempPathErrorFile));
            Files.delete(Paths.get(tempPathBatchFile));
            Files.delete(Paths.get(resourcePath));
            //5.删除batch文件
            zhiPuAiApi.batchFileDelete(context.getZhiPuStatusAiModel().getInputFileId());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void extracted(String purpose, Path jsonlfile, List<String> customIds, String upBatchId) {
        UploadFileRequest request = UploadFileRequest.builder()
                .purpose(purpose)
                .filePath(jsonlfile.toString())
                .build();
        ModLlmBatchInfoRecord batchInfoRecord = new ModLlmBatchInfoRecord();
        batchInfoRecord.setInputFileName(FileUtil.getName(jsonlfile));
        batchInfoRecord.setBatchSize(customIds.size());
        batchInfoRecord.setCreateTime(LocalDateTime.now());
        batchInfoRecord.setUpdateTime(LocalDateTime.now());
        batchInfoRecord.setErrorBatchId(upBatchId);
        batchInfoRecord.setErrorNum(1);


        FileApiResponse fileApiResponse = null;
        try {
            fileApiResponse = zhiPuAiApi.invokeUploadFileApi(request);
        } catch (Exception e) {
            log.error("上传文件失败！：{}", e.getMessage());
            batchInfoRecord.setBatchStatus(BatchStatusEnum.FILE_NOT_UPLOADED.getCode());
            batchInfoRecordMapper.insert(batchInfoRecord);
            e.printStackTrace();
            return;
        }
        if (fileApiResponse.isSuccess()) {
            batchInfoRecord.setInputFileId(fileApiResponse.getData().getId());
            BatchResponse batchResponse = null;
            try {
                batchResponse = zhiPuAiApi.batchesCreate(fileApiResponse.getData().getId());
            } catch (Exception e) {
                log.error("创建Batch失败！：{}", e.getMessage());
                batchInfoRecord.setBatchStatus(BatchStatusEnum.BATCH_NOT_CREATED.getCode());
                batchInfoRecordMapper.insert(batchInfoRecord);
                e.printStackTrace();
                return;
            }
            if (batchResponse.isSuccess()) {
                batchInfoRecord.setBatchStatus(BatchStatusEnum.VALIDATING.getCode());
                batchInfoRecord.setBatchId(batchResponse.getData().getId());
                log.debug("创建Batch成功:{}", batchResponse.getData().getId());
            } else {
                batchInfoRecord.setBatchStatus(BatchStatusEnum.BATCH_NOT_CREATED.getCode());
                log.debug("创建Batch失败:{}", jsonlfile.toString());
            }
            log.debug("上传文件成功:{}", jsonlfile.toString());

        } else {
            log.debug("上传文件失败:{}", jsonlfile.toString());
            batchInfoRecord.setBatchStatus(BatchStatusEnum.FILE_NOT_UPLOADED.getCode());
        }

        batchInfoRecordMapper.insert(batchInfoRecord);
        try {
            uploadFileService.putObject(getFileName(FileUtil.getName(jsonlfile)), Files.newInputStream(jsonlfile));
        } catch (Exception e) {
            e.printStackTrace();
            log.debug("保存文件失败：{}", e.getMessage());
        }

        List<ModLlmBatchInfoRecordDetailedModel> batchInfoRecordDetailed = IntStream.range(0, customIds.size())
                .mapToObj(index -> new ModLlmBatchInfoRecordDetailedModel(customIds.get(index), batchInfoRecord.getNewId()))
                .collect(Collectors.toList());
        try {
            analysisProducer.pushBatchContentIds(batchInfoRecordDetailed);
        } catch (Exception e) {
            log.error("kafka producer batch info content record error: {}", e.getMessage());
            e.printStackTrace();
        }

    }

    private String getFileName(String name) {
        return ServiceContextHolder.getSystemId().concat("/").concat("zhipuai-upload-file").concat("/").concat(name);
    }

    private static Integer extracted(String tempPathBatchFile, String resourcePath, List<String> customIds) {
        // 读取文件，并过滤掉不在List中的custom_id
        List<String> filteredLines = new ArrayList<>();
        try {
            // 输入jsonl文件路径
            Path inputPath = Paths.get(tempPathBatchFile);

            Path outputPath = Paths.get(resourcePath);


            try (BufferedReader reader = Files.newBufferedReader(inputPath)) {
                String line;
                while ((line = reader.readLine()) != null) {
                    // 检查每行是否包含有效的custom_id
                    for (String id : customIds) {
                        if (line.contains(id)) {
                            filteredLines.add(line);
                            break;
                        }
                    }
                }
            }

            // 将过滤后的内容写入新文件
            try (BufferedWriter writer = Files.newBufferedWriter(outputPath)) {
                for (String filteredLine : filteredLines) {
                    writer.write(filteredLine);
                    writer.newLine(); // 添加换行符，确保格式仍然是jsonl
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return filteredLines.size();

    }

    private static void extracted(String tempPathErrorFile, List<String> customIds) {
        // 创建固定大小的线程池
        int numberOfThreads = Runtime.getRuntime().availableProcessors();
        ExecutorService executorService = Executors.newFixedThreadPool(numberOfThreads);

        try (BufferedReader reader = new BufferedReader(new FileReader(tempPathErrorFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 提交任务到线程池
                String finalLine = line;
                executorService.submit(() -> {
                    try {
                        JSONObject jsonObject = new JSONObject(finalLine);
                        String customId = jsonObject.getString("custom_id");
                        synchronized (customIds) {
                            customIds.add(customId);
                        }
                    } catch (JSONException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 关闭线程池并等待所有任务完成
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    @Override
    public boolean isAccess() {
        ThirdPartyAIContext context = this.getRequestData();
//        Assert.isTrue(StrUtil.isNotBlank(context.getClientId()), "getClientId cannot be empty");
//        Assert.isTrue(CollUtil.isNotEmpty(context.getIds()), "getIds cannot be empty");


        return true;
    }

}

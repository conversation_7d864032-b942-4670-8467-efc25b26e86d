package com.voc.service.model.impl.cenvert;

import com.voc.service.model.entity.*;
import com.voc.service.model.model.*;
import com.voc.service.model.vo.ModLlmBatchInfoRecordVo;
import com.voc.service.model.vo.ModLlmPromptTemplateVo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * @Title: AysConvertMapperService
 * @Package: com.voc.service.analysis.core.v2.impl.cenvert
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 11:54
 * @Version:1.0
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ModelConvertMapperService {
    List<CarAndDimChannelMappingModel> cenvertToCarAndDimChannelMappingModelList(List<CarAndDimChannelMappingEntity> list);

    List<ModLlmReturnResultInfoRecordEntity> cenvertToModResultRecordEntityList(List<ModLlmReturnResultInfoRecordModel> list);

    List<MergingResultDataEntity> cenvertToMergingResltEntityList(List<MergingResultDataModel> list);

    List<MergeStatusRecordEntity> cenvertToMergeStatusRecordEntityList(List<MergeStatusRecordModel> mergeStatusRecordModelList);

    List<ModMetaDataAnalysisModel> cenvertToModMetaDataAnalysisEntityList(List<ModMetaDataAnalysisEntity> list);

    List<ModFtmBrandCarSeriesEntity> cenvertToModFtmBrandCarSeriesEntityList(List<ModFtmBrandCarSeriesModel> list);

    List<ModFtmBrandCarSeriesModel> cenvertToModFtmBrandCarSeriesModeList(List<ModFtmBrandCarSeriesEntity> list);

    List<MergingResultDataModel> cenvertToMergingResultDataModelList(List<MergingResultDataEntity> list);

    List<ModCaTagResultEntity> cenvertToModCaTagResultEntityList(List<ModCaTagResultModel> model);

    List<ModCarSceneResultEntity> cenvertToModCarSceneResultEntityList(List<ModCarSceneResultModel> list);

    List<ModCaTagResultModel> cenvertToModCaTagResultModelList(List<ModCaTagResultEntity> model);

    List<ModCarNotSceneResultEntity> cenvertToModCarNotSceneResultEntityList(List<ModCarNotSceneResultModel> list);

    List<ModCarSceneResultModel> cenvertToModCarSceneResultModelList(List<ModCarSceneResultEntity> list);

    List<CarAndDimOpinionMappingModel> cenvertToCarAndDimOpinionMappingModelList(List<CarAndDimOpinionMappingEntity> list);

    ModLlmPromptTemplateVo converToPromptTemplateToVo(ModLlmPromptTemplate entity);

    List<ScenariosTagsLevelMappingModel> cenvertToScenariosTagsLevelMappingModelList(List<ScenariosTagsLevelMappingEntity> list);

    List<ModLlmReturnResultInfoRecordModel> cenvertToModLlmReturnResultInfoRecordEntityList(List<ModLlmReturnResultInfoRecordEntity> list);

    List<ModLlmReturnResultExceptionRecordEntity> cenvertToModLlmReturnExceptionRecordModelList(List<ModLlmReturnResultExceptionRecordModel> list);

    List<ModLlmPromptTemplateVo> cenvertToModLlmPromptTemplateVoList(List<ModLlmPromptTemplate> ch1);

    List<ModCaNotTagResultModel> cenvertToModCarNotTagResultList(List<ModCaNotTagResultEntity> modCaNotTagResultEntities);

    List<ModCaNotTagResultEntity> cenvertToModCarNotTagResultEntityList(List<ModCaNotTagResultModel> modCaNotTagResultEntities);

    ModLlmBatchInfoRecordVo convertToModLlmBatchInfoRecordVo(ModLlmBatchInfoRecord entity);

    List<ModLlmBatchInfoRecordVo> convertToModLlmBatchInfoRecordEntityListVo(List<ModLlmBatchInfoRecord> entity);

    List<ModScenariosNotTagResultEntity> cenvertToModScenariosNotTagResultEntityList(List<ModScenariosNotTagResultModel> list);

    List<ModRequestAiStatusEntity> cenvertToModRequestAiStatusEntityList(List<ModRequestAiModel> list);

    List<MergingResultDataModel> cenvertToMergingResultDataModellList(List<MergeStatusRecordEntity> entity);

    List<ModLlmBatchInfoRecordDetailed> cenvertToModLlmBatchInfoRecordDetailedModelList(List<ModLlmBatchInfoRecordDetailedModel> list);

    MergeStatusRecordEntity cenvertToMergeStatusRecordEntity(MergeStatusRecordModel model);

    MergeStatusRecordModel cenvertToMergeStatusRecordModel(MergeStatusRecordEntity entity);

    List<OpinionAndTagsMappingModel> cenvertToViewpointAndTagsMappingModelList(List<OpinionAndTagsMappingEntity> viewpointAndTagsMappingEntities);
}

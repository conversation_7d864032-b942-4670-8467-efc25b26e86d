package com.voc.service.model.nodes;


import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "validateMetaDataNode", name = "校验原数据有效性节点")
public class ValidateMetaDataNode extends AbstractNodeIf {

    @Override
    public boolean processIf() throws Exception {
        try {
            ThirdPartyAIContext context = this.getRequestData();
//            final Set<String> paramIds = context.getIds();

            return true;
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

}

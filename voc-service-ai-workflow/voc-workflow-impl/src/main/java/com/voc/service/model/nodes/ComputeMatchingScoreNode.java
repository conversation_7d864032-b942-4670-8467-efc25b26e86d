package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.voc.service.model.api.IModelMilvusResultDataService;
import com.voc.service.model.model.VoiceClipRequestDataModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ModelContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.List;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "computeMatchingScoreNode", name = "计算ONNX数据值节点")
public class ComputeMatchingScoreNode extends AbstractNode {

    private static final Logger log = LoggerFactory.getLogger(ComputeMatchingScoreNode.class);
    @Autowired
    private IModelMilvusResultDataService modelMilvusResultDataService;

    @Override
    public void process() throws Exception {
        ModelContext context = this.getRequestData();
        try {
            final VoiceClipRequestDataModel processingDataset = context.getInputDataset();
            //调用onnxRuntime模型计算近似值
            final List<Float> onnxRuntimeEmbeddingData = modelMilvusResultDataService.findOnnxRuntimeEmbeddingData(processingDataset);
            if (CollUtil.isEmpty(onnxRuntimeEmbeddingData)) {
                log.error("onnx计算结果异常! {}", processingDataset);
//                throw new Exception("onnx计算结果异常! ");
            }
            //设置ONNX计算结果
            context.setEmbeddingList(onnxRuntimeEmbeddingData);
        } catch (Exception e) {
            log.error("onnx计算结果异常! {}", e.getMessage());
            context.setEmbeddingList(List.of());
//            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        ModelContext context = this.getRequestData();
        Assert.isTrue(ObjectUtil.isNotEmpty(context.getInputDataset()), "getProcessingDataset cannot be empty");
//        Assert.isTrue(CollUtil.isNotEmpty(context.getIds()), "getIds cannot be empty");


        return true;
    }

}

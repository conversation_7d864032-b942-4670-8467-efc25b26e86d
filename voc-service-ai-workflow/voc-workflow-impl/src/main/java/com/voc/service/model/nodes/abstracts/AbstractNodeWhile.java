package com.voc.service.model.nodes.abstracts;

import com.yomahub.liteflow.core.NodeWhileComponent;

/**
 * @Title: AbstractNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:33
 * @Version:1.0
 */
public abstract class AbstractNodeWhile extends NodeWhileComponent {
    /*@Override
    public void onError(Exception e) throws Exception {
        AbstractContext context = this.getRequestData();
        context.getStopWatch().stop();
        context.getStopWatch().start(String.format("异常↓↓ [%s]节点：%s, dataId=%s", this.getName(), e.getMessage(),context.getDataId()));
        context.getStopWatch().stop();
        log.error("{}异常 {}", this.getName(), e.getMessage());
        this.getSlot().setException(e);
        //是否结束整个流程
        super.setIsEnd(true);
    }

    @Override
    public void onSuccess() throws Exception {
        super.onSuccess();
    }

    @Override
    public void beforeProcess() {
        super.beforeProcess();
        AbstractContext context = this.getRequestData();
        context.getStopWatch().stop();
        context.getStopWatch().start(String.format("[%s%s]节点.dataId=%s", this.getName()
                , StrUtil.isNotBlank(this.getTag()) ? ":".concat(this.getTag()) : "", context.getDataId()));

    }*/


}

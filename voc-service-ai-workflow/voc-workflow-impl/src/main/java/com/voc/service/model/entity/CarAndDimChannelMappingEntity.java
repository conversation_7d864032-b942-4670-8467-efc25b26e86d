package com.voc.service.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Title: CarAndDimChannelMappingEntity
 * @Package: com.voc.service.model.entity
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 16:38
 * @Version:1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "mod_car_and_dim_channel_mapping")
public class CarAndDimChannelMappingEntity implements Serializable {
    String car;
    String dim;
    String tagName;
    String tagCode;
    String tagType;
    String severityLevel;
}

package com.voc.service.model.nodes;


import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.retry.RetryException;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "isExistUnprocessedDatasetNode", name = "检查是否有未完成处理的数据节点")
public class IsExistUnprocessedDatasetNode extends AbstractNodeIf {

    @Override
    public boolean processIf() throws Exception {
        try {
            ThirdPartyAIContext context = this.getRequestData();

            return true;
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }

}

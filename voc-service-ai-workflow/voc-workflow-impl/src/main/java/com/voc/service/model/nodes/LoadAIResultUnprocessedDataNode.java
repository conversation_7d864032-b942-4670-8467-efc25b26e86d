package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.api.IModLlmReturnResultInfoRecordService;
import com.voc.service.model.model.ModLlmReturnResultInfoRecordModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "loadAIResultUnprocessedDataNode", name = "查询IDS对应的db数据节点")
public class LoadAIResultUnprocessedDataNode extends AbstractNode {

    private static final Logger log = LoggerFactory.getLogger(LoadAIResultUnprocessedDataNode.class);
    @Autowired
    IModLlmReturnResultInfoRecordService iModLlmReturnResultInfoRecordService;
    @Autowired
    Executor executor;
    @Autowired
    IMergeStatusRecordService mergeStatusRecordService;

    @Override
    public void process() throws Exception {
        try {
            AIResultDataContext context = this.getRequestData();
            final Set<String> idsParam = this.getPrivateDeliveryData();
            Assert.isTrue(CollUtil.isNotEmpty(idsParam), "getPrivateDeliveryData  cannot be empty");

            final List<ModLlmReturnResultInfoRecordModel> modelList
                    = iModLlmReturnResultInfoRecordService.findByIds(idsParam);
            //预加载数据集
            /*ServiceContextHolder.getExecutor().execute(() -> {
                try {
                    mergeStatusRecordService.preLoadDatase(idsParam);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });*/

            if (CollUtil.isEmpty(modelList)) {
                return;
            }
            log.info("本次执行的数据集大小 ={}", modelList.size());

            final List<String> newIdList = modelList.stream().map(ModLlmReturnResultInfoRecordModel::getNewId).collect(Collectors.toList());

            context.setWorkId(modelList.get(0).getWorkId());
            context.setClientId(modelList.get(0).getClientId());
            context.setDataId(modelList.get(0).getNewId());
            context.setContentType(modelList.get(0).getContentType());
            context.setChannelId(modelList.get(0).getChannelId());
            context.setModelType(modelList.get(0).getModelType());
            context.setBrandCarDataList(modelList);
            context.setOpinionDataList(modelList);
            context.setScenarioDataList(modelList);
            log.info("AI返回结果本次需处理数据量: {}", newIdList.size());

            //异常场景
            if (CollUtil.isEmpty(context.getBrandCarDataList()) && CollUtil.isEmpty(context.getOpinionDataList()) && CollUtil.isEmpty(context.getScenarioDataList())) {
                log.warn("AI返回结果本次需处理数据量: 0");
                return;
            }
            if (CollUtil.isNotEmpty(newIdList)) {
                this.sendPrivateDeliveryData("addRedisStatusDataNode", newIdList);
            }
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        Assert.isTrue(ObjUtil.isNotNull(this.getRequestData()), "getRequestData cannot be empty");

        return true;
    }

}

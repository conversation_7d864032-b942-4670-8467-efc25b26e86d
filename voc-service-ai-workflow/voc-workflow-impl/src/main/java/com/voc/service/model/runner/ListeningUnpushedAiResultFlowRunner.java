package com.voc.service.model.runner;

import com.voc.service.common.util.IdWorker;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;

/**
 * @Title: ListeningAiResultFlowRunner
 * @Package: com.voc.service.analysis.core.v2.runner
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 16:14
 * @Version:1.0
 */
@Component
//@Order(value = 11)
public class ListeningUnpushedAiResultFlowRunner/* implements CommandLineRunner*/ {
    private static final Logger log = LoggerFactory.getLogger(ListeningUnpushedAiResultFlowRunner.class);
    @Resource
    private FlowExecutor flowExecutor;
    @Autowired
    Executor executor;
//    final String chainId = "invoking_raw_normalizer_flow";
    final String chainId = "invoking_raw_normalizer_flow_v2";
    @Resource
    private RedissonClient redissonClient;
    RLock rlock;

    @PostConstruct
    public void init() {
        rlock = redissonClient.getLock(chainId);
    }

    public ListeningUnpushedAiResultFlowRunner() {
        log.info("--->> {}", this.getClass().getSimpleName());
    }

    @Scheduled(cron = "0/5 * * * * ?")
    @XxlJob("listeningUnpushedAiResultFlowRunner")
    public void run() throws Exception {
        /**
         * 默认开启AI服务处理结果监听
         */
        AIResultDataContext context = AIResultDataContext.builder().build();
        try {
            if (!rlock.isLocked()) {
                rlock.lock();
                context.setWorkId(IdWorker.getId());
                context.getStopWatch().start("执行 ".concat(chainId));
                LiteflowResponse response = flowExecutor.execute2Resp(chainId, context,context.getWorkId());
                if (!response.isSuccess()) {
                    log.error("workId:{}  {}", context.getWorkId(), response.getCause());
                    throw response.getCause();
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (rlock.isHeldByCurrentThread()) {
                rlock.unlock();
            }
            log.info("chainId {} 完成 ", chainId);
//            context.getStopWatch().prettyPrint();
        }
    }
}

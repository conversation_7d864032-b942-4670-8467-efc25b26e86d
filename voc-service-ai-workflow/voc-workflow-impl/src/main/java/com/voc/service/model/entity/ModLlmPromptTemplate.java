package com.voc.service.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 大模型prompt提示词模板表
 *
 * @TableName mod_llm_prompt_template
 */
@TableName(value = "mod_llm_prompt_template")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModLlmPromptTemplate implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "new_id", type = IdType.ASSIGN_UUID)
    private String newId;

    /**
     * 客户标识
     */
    private String clientId;

    /**
     * 请求类型：POST
     */
    private String method;

    /**
     * 请求地址
     */
    private String url;

    /**
     * 请求模型名称
     */
    private String model;

    /**
     * prompt
     */
    private String messages;

    /**
     * prompt状态 0可用，1不可用
     */
    private Integer promptStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 接收处理标识
     */
    private String workId;

    private String source;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}

package com.voc.service.model.nodes;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.voc.service.model.model.VoiceClipRequestDataModel;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.ModelContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.RetryException;
import org.springframework.util.Assert;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "isExistMatchingSchemaDataNode", name = "检查是否查询到结果节点")
public class IsExistMatchingSchemaDataNode extends AbstractNodeIf {

    private static final Logger log = LoggerFactory.getLogger(IsExistMatchingSchemaDataNode.class);

    @Override
    public boolean processIf() throws Exception {
        try {
            ModelContext context = this.getRequestData();
            final VoiceClipRequestDataModel processingDataset = context.getInputDataset();
            if ("opinion".equalsIgnoreCase(context.getTag())) {
                if (ObjectUtils.isEmpty(context.getMilvusResultData())) {
                    log.info("未从向量数据库opinion中匹配到声音片段的近似结果");
                    return false;
                }
            } else if ("scenario".equalsIgnoreCase(context.getTag())) {
                if (ObjectUtils.isEmpty(context.getMilvusResultData())) {
                    log.info("未从向量数据库scenario中匹配到场景的近似结果");
                    return false;
                }
            } else {
                throw new Exception("未找到匹配tag值");
            }
            return true;
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        ModelContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getTag()), "getTag cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(context.getContentId()), "getContentId cannot be empty");
        Assert.isTrue(ObjectUtil.isNotEmpty(context.getProcessingLabeledDataset()), "getProcessingDataset cannot be empty");
//        Assert.isTrue(CollUtil.isNotEmpty(context.getIds()), "getIds cannot be empty");


        return true;
    }

}

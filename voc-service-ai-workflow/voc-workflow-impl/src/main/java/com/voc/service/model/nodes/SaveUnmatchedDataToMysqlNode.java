package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.voc.service.model.api.IModCarNotSceneResultService;
import com.voc.service.model.api.IModCarNotTagResultService;
import com.voc.service.model.model.ModCaNotTagResultModel;
import com.voc.service.model.model.ModCarNotSceneResultModel;
import com.voc.service.model.model.VoiceClipRequestDataModel;
import com.voc.service.model.model.VoiceClipResultData;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ModelContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryException;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "saveUnmatchedDataToDBNode", name = "保存未命中数据到mysql节点")
public class SaveUnmatchedDataToMysqlNode extends AbstractNode {
    @Autowired
    IModCarNotTagResultService modCarNotTagResultService;
    @Autowired
    IModCarNotSceneResultService modCarNotSceneResultService;

    @Override
    public void process() throws Exception {
        try {
            ModelContext context = this.getRequestData();
            VoiceClipRequestDataModel inputDataset = context.getInputDataset();
            String clientId = context.getClientId();
            List<VoiceClipResultData> processingUnLabeledDataset = context.getProcessingUnLabeledDataset();
            VoiceClipResultData voiceClipResultData = processingUnLabeledDataset.get(0);
            Map<String, Object> extAttrMaps = new HashMap<>();
            if ("opinion".equalsIgnoreCase(context.getTag())) {
                extAttrMaps.put("evalAttrs", StrUtil.isNotBlank(inputDataset.getAspect()) ? inputDataset.getAspect() : "");
                extAttrMaps.put("evalSubject", StrUtil.isNotBlank(inputDataset.getSubject()) ? inputDataset.getSubject() : "");
                extAttrMaps.put("evalDesc", StrUtil.isNotBlank(inputDataset.getDesc()) ? inputDataset.getDesc() : "");
                extAttrMaps.put("normalizedOpinion", StrUtil.isNotBlank(voiceClipResultData.getSim_opinion()) ? voiceClipResultData.getSim_opinion() : "");
                extAttrMaps.put("similarity", ObjectUtils.isNotEmpty(voiceClipResultData.getScore()) ? voiceClipResultData.getScore() : "");
                // 保存opinion未命中数据
                ModCaNotTagResultModel build = ModCaNotTagResultModel.builder()
                        .newId(inputDataset.getNew_id())
                        .voiceId(inputDataset.getNew_id())
                        .originalId(context.getContentId())
                        .channelId(context.getChannelId())
                        .contentType(context.getContentType())
                        .modelType(context.getModelType())
                        .workId(context.getWorkId())
                        .subject(inputDataset.getSubject())
                        .description(inputDataset.getDesc())
                        .opinion(inputDataset.getSubject() + inputDataset.getDesc())
                        .carBodyLabel(voiceClipResultData.getCar_schema())
                        .viewLabel(voiceClipResultData.getEvaluate_schema())
                        .opinionSentiment(inputDataset.getSentiment())
                        .processStatus(context.getNodeIdentifier())
                        .extFields(extAttrMaps)
                        .build();
                modCarNotTagResultService.add(clientId, Collections.singletonList(build));
            } else if ("scenario".equalsIgnoreCase(context.getTag())) {
                // 保存scenario未命中数据
                ModCarNotSceneResultModel build = ModCarNotSceneResultModel.builder()
//                        .newId(IdWorker.getId())
                        .newId(inputDataset.getNew_id())
                        .voiceId(inputDataset.getNew_id())
                        .originalId(context.getContentId())
                        .workId(context.getWorkId())
                        .channelId(context.getChannelId())
                        .contentType(context.getContentType())
                        .modelType(context.getModelType())
                        .scenario(inputDataset.getScenario())
                        .tagsLevel4(voiceClipResultData.getTagsLevel4())
                        .build();
                modCarNotSceneResultService.add(clientId, Collections.singletonList(build));
            } else {
                throw new Exception("未找到匹配tag值");
            }
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        ModelContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getTag()), "getTag cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(context.getWorkId()), "getWorkId cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(context.getContentId()), "getContentId cannot be empty");
        Assert.isTrue(CollUtil.isNotEmpty(context.getProcessingUnLabeledDataset()), "getProcessingUnlabeledDataset cannot be empty");


        return true;
    }

}

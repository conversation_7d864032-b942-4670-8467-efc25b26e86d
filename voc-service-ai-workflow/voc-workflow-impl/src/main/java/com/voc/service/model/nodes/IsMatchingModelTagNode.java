package com.voc.service.model.nodes;


import cn.hutool.core.util.StrUtil;
import com.voc.service.model.model.VoiceClipResultData;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.ModelContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.RetryException;
import org.springframework.util.Assert;

import java.util.List;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "isMatchingModelTagNode", name = "检查当前流程tag类型节点")
public class IsMatchingModelTagNode extends AbstractNodeIf {
    private static final Logger log = LoggerFactory.getLogger(IsMatchingModelTagNode.class);

    @Override
    public boolean processIf() throws Exception {
        try {
            ModelContext context = this.getRequestData();
            List<VoiceClipResultData> processingLabeledDataset = context.getProcessingLabeledDataset();
            if ("opinion".equalsIgnoreCase(this.getTag()) && this.getTag().equalsIgnoreCase(context.getTag())) {
                if (ObjectUtils.isNotEmpty(processingLabeledDataset)) {
                    return true;
                }
                log.info("{}有可用schema 数据", this.getTag());
            } else if ("scenario".equalsIgnoreCase(this.getTag()) && this.getTag().equalsIgnoreCase(context.getTag())) {
                if (ObjectUtils.isNotEmpty(processingLabeledDataset)) {
                    return true;
                }
                log.info("{}有可用schema 数据", this.getTag());
            }
            log.info("{}无可用schema数据", this.getTag());
            return false;
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        ModelContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(this.getTag()), "getTag cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(context.getTag()), "getTag cannot be empty");


        return true;
    }

}

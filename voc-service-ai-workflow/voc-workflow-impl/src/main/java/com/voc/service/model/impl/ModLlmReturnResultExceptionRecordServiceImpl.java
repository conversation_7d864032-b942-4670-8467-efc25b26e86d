package com.voc.service.model.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.model.api.IModLlmReturnResultExceptionRecordService;
import com.voc.service.model.entity.ModLlmReturnResultExceptionRecordEntity;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.ModLlmReturnResultExceptionRecordMapper;
import com.voc.service.model.model.ModLlmReturnResultExceptionRecordModel;
import com.voc.service.model.producers.kafka.ModelResultProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class ModLlmReturnResultExceptionRecordServiceImpl extends ServiceImpl<ModLlmReturnResultExceptionRecordMapper, ModLlmReturnResultExceptionRecordEntity>
        implements IModLlmReturnResultExceptionRecordService {

    @Autowired
    ModelConvertMapperService modelConvertMapperService;


    @Autowired
    ModelResultProducer modelResultProducer;

    @Override
    public int saveReturnResultExceptionData(List<ModLlmReturnResultExceptionRecordModel> recordModelList) {
        if (CollectionUtil.isEmpty(recordModelList)) {
            return 0;
        }
        List<ModLlmReturnResultExceptionRecordEntity> modLlmReturnResultExceptionRecordEntities = modelConvertMapperService.cenvertToModLlmReturnExceptionRecordModelList(recordModelList);
        modelResultProducer.pushLlmReturnExceptionData(MessageDTO.builder().source("").data(modLlmReturnResultExceptionRecordEntities).build());
        return recordModelList.size();
    }

}





package com.voc.service.model.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.model.api.IModLlmPromptTemplateService;
import com.voc.service.model.entity.ModLlmPromptTemplate;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.ModLlmPromptTemplateMapper;
import com.voc.service.model.vo.ModLlmPromptTemplateVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【mod_llm_prompt_template(大模型prompt提示词模板表)】的数据库操作Service实现
 * @createDate 2024-08-01 13:45:36
 */
@Service
public class ModLlmPromptTemplateServiceImpl extends ServiceImpl<ModLlmPromptTemplateMapper, ModLlmPromptTemplate>
        implements IModLlmPromptTemplateService {
    @Autowired
    ModelConvertMapperService convertMapperService;

    @Override
    public ModLlmPromptTemplateVo getTemplateId(String templateId) {
        baseMapper.selectById("00893c3ac619ca96423e64d2ade938b1");
        ModLlmPromptTemplateVo vo = new ModLlmPromptTemplateVo();
        ModLlmPromptTemplate entity = this.getById(templateId);
        return convertMapperService.converToPromptTemplateToVo(entity);
    }

    @Override
    public Map<String, ModLlmPromptTemplateVo> getAllPromptTemplate() {
        QueryWrapper<ModLlmPromptTemplate> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ModLlmPromptTemplate::getPromptStatus, 0);
        List<ModLlmPromptTemplate> ch1 = this.list(wrapper);
        List<ModLlmPromptTemplateVo> ch2 = convertMapperService.cenvertToModLlmPromptTemplateVoList(ch1);
        Map<String, ModLlmPromptTemplateVo> promptTemplateMap = ch2.stream().collect(Collectors.toMap(ModLlmPromptTemplateVo::getSource, Function.identity()));
        return promptTemplateMap;
    }
}





package com.voc.service.model.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.model.api.IModRequestAiStatusService;
import com.voc.service.model.entity.ModRequestAiStatusEntity;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.ModRequestAiStatusMapper;
import com.voc.service.model.model.ModRequestAiModel;
import com.voc.service.model.model.ModRequestAiStatusModel;
import com.voc.service.model.producers.kafka.ModelResultProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Title: ModCaTagResultServiceImpl
 * @Package: com.voc.service.model.impl
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/6 10:14
 * @Version:1.0
 */
@Service
public class ModRequestAiStatusServiceImpl extends ServiceImpl<ModRequestAiStatusMapper, ModRequestAiStatusEntity>
        implements IModRequestAiStatusService {

    @Autowired
    ModelConvertMapperService modelConvertMapperService;

    @Autowired
    ModelResultProducer modelResultProducer;

    @Override
    public List<ModRequestAiStatusModel> findStatusData(List<String> idList) {
        return this.baseMapper.findStatusData(idList);
    }

    @Override
    public List<ModRequestAiStatusModel> findStatusList(List<String> idList) {
        return this.baseMapper.findStatusList(idList);
    }

    @Override
    public Boolean add(List<ModRequestAiModel> modelList) {
        if (CollUtil.isEmpty(modelList)) {
            return false;
        }
        List<ModRequestAiStatusEntity> entity = modelConvertMapperService.cenvertToModRequestAiStatusEntityList(modelList);
        return this.saveBatch(entity);
    }
}

package com.voc.service.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "mod_meta_data_analysis")
public class ModMetaDataAnalysisEntity implements Serializable {

    private String newId; // 主键
    private String workId; // 接收处理标识
    private String clientId; // 客户标识
    private String channelId; // 渠道标识
    private String contentType; // 内容类型
    private String modelType;
    private String extFields;
    private String title; // 标题
    private String content; // 内容
    private Integer done; // 是否完成计算
    private Integer dataStatus; // 数据状态
    private LocalDateTime publishTime; // 发布时间
    private LocalDateTime createTime; // 接收时间
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voc.service.model.mapper.CarAndDimChannelMappingMapper">

    <select id="selectcarAndDimMappings" parameterType="com.voc.service.model.entity.CarAndDimChannelMappingEntity" >
		select
			qut.car,  qut.dim  ,qut.quality_tag_name  as tag_name ,qut.quality_tag_code  as tag_code ,qut.severity_level, '1' as tag_type
		from mod_car_and_dim_quality_tag_mapping qut
		union all
		select biz.car,  biz.dim  ,biz.business_tag_name  as tag_name ,biz.business_tag_code  as tag_code , '' as severity_level, '2' as tag_type
		from mod_car_and_dim_buz_tag_mapping biz
    </select>

</mapper>
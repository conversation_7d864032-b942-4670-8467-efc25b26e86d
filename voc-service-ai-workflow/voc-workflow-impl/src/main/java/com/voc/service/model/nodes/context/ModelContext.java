package com.voc.service.model.nodes.context;

import com.voc.service.model.model.CarAndDimChannelMappingModel;
import com.voc.service.model.model.VoiceClipRequestDataModel;
import com.voc.service.model.model.VoiceClipResultData;
import com.voc.service.model.nodes.abstracts.AbstractContext;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Title: AiWorkflowDefaultContext
 * @Package: com.voc.service.model.nodes.context
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 14:28
 * @Version:1.0
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
public class ModelContext extends AbstractContext implements Serializable {
    String tag;
    @Builder.Default
    VoiceClipRequestDataModel inputDataset = VoiceClipRequestDataModel.builder().build();

    /**
     * OnnxRuntime 生成的向量
     */
    @Builder.Default
    private List<Float> embeddingList = new ArrayList<>();
    /**
     * milvus向量库查询结果
     */
    @Builder.Default
    List<VoiceClipResultData> milvusResultData = new ArrayList<>();

    //一个声音对应多个topic
    @Builder.Default
    List<VoiceClipResultData> processingLabeledDataset = new ArrayList<>();

    @Builder.Default
    List<VoiceClipResultData> processingUnLabeledDataset = new ArrayList<>();
    String contentId;

    String clientId;

    @Schema(description = "渠道Id")
    String channelId;
    @Schema(description = "文章类型")
    String contentType;
    @Schema(description = "模型类型")
    Integer modelType;

    @Builder.Default
    List<CarAndDimChannelMappingModel> modelList = new ArrayList<>();
    /**
     * 节点标识 用于记录未命中数据产生的节点
     */
    String nodeIdentifier;
}

package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollUtil;
import com.voc.service.model.api.IAnalysisService;
import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.api.IModLlmReturnResultInfoRecordService;
import com.voc.service.model.model.MergeStatusRecordModel;
import com.voc.service.model.model.ModWorkflowModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "modifyResultDataStatusNode", name = "修改合并后数据推送完成状态节点")
public class ModifyResultDataStatusNode extends AbstractNode {

    private static final Logger log = LoggerFactory.getLogger(ModifyResultDataStatusNode.class);
    @Autowired
    IAnalysisService analysisService;
    @Autowired
    IModLlmReturnResultInfoRecordService modLlmReturnResultInfoRecordService;
    @Autowired
    IMergeStatusRecordService mergeStatusRecordService;

    @Override
    public void process() throws Exception {
        try {
            final Map<Integer, List<ModWorkflowModel>> map = this.getPrivateDeliveryData();
            if (CollUtil.isEmpty(map)) {
                log.error("modifyResultDataStatusNode接收参数为空，跳过执行！");
                return;
            }
            //记录已完成所有流程的id集合
            Set<String> doneIdsList = new HashSet<>();
            //修改redis 状态， 返回满足条件的数据id
            map.keySet().forEach(key -> {
                final Set<String> ids = map.get(key).stream().map(ModWorkflowModel::getVoiceId).collect(Collectors.toSet());
                doneIdsList.addAll(ids);
            });
            List<MergeStatusRecordModel> modelListCahce = mergeStatusRecordService.getModelListCahce(doneIdsList);
            //触发修改状态事件监控：  查询数据的修改状态，完成修改后删除redis key
            for (MergeStatusRecordModel model : modelListCahce) {
                if ("opinion".equalsIgnoreCase(this.getTag()) && model.getLabelNum() == -1) {
                    log.info("opinion异常数据补充:{}", model.getNewId());
                    model.setLabelNum(0);
                    mergeStatusRecordService.updateLabeledDataSize(Set.of(model.getNewId()), 0);
                }
                if ("scenario".equalsIgnoreCase(this.getTag()) && model.getScenarioNum() == -1) {
                    log.info("scenario异常数据补充:{}", model.getNewId());
                    model.setScenarioNum(0);
                    mergeStatusRecordService.updateScenariosDataSize(Set.of(model.getNewId()), 0);
                }
                if ("brand".equalsIgnoreCase(this.getTag()) && model.getBrandCarNum() == -1) {
                    log.info("brand异常数据补充:{}", model.getNewId());
                    model.setBrandCarNum(0);
                    mergeStatusRecordService.updateBrandDataSize(Set.of(model.getNewId()), 0);
                }
            }
            List<MergeStatusRecordModel> mergeStatusRecordModels = modelListCahce.stream().
                    filter(entity -> entity.getLabelNum() != -1 && entity.getScenarioNum() != -1 && entity.getBrandCarNum() != -1).toList();
            if (CollUtil.isNotEmpty(mergeStatusRecordModels)) {
                log.info("满足条件，则修改声音数据状态，并删除对应redis key {}", doneIdsList);
                //满足条件，则修改声音数据状态，并删除对应redis key
                List<String> ids = mergeStatusRecordModels.stream().map(MergeStatusRecordModel::getNewId).collect(Collectors.toList());
                modLlmReturnResultInfoRecordService.modifyToDone(ids);
            }
        } catch (Exception e) {
            log.info("modifyResultDataStatusNode异常");
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        AIResultDataContext context = this.getRequestData();

        return true;
    }

}

package com.voc.service.model.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.model.api.IModScenariosNotTagResultService;
import com.voc.service.model.entity.ModScenariosNotTagResultEntity;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.ModScenariosNotTagResultMapper;
import com.voc.service.model.model.ModScenariosNotTagResultModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Title: ModCaTagResultServiceImpl
 * @Package: com.voc.service.model.impl
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/6 10:14
 * @Version:1.0
 */
@Service
public class ModScenariosNotTagResultServiceImpl extends ServiceImpl<ModScenariosNotTagResultMapper, ModScenariosNotTagResultEntity>
        implements IModScenariosNotTagResultService {

    @Autowired
    ModelConvertMapperService modelConvertMapperService;

    @Override
    public boolean add(List<ModScenariosNotTagResultModel> list) {
        if (CollUtil.isEmpty(list)) {
            return false;
        }
        List<ModScenariosNotTagResultEntity> entity = modelConvertMapperService.cenvertToModScenariosNotTagResultEntityList(list);

        return this.saveBatch(entity);
    }
}

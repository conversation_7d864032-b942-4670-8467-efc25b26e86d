<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voc.service.model.mapper.ModLlmReturnResultInfoRecordMapper">

   <!-- <select id="findUntreatedData" parameterType="com.voc.service.model.entity.ModLlmReturnResultInfoRecordEntity" >
		with comm_data as (
			select original_id, client_id  from mod_llm_return_result_info_record
			where
				create_time &gt;= days_sub(CURRENT_DATE(),2)
			  and done = 0
			group by original_id,client_id
			limit #{pushNormalizerFlowDatasetSize}
		)
		select  f.new_id, f.original_id, f.work_id, f.client_id from mod_llm_return_result_info_record f, comm_data c
		where f.original_id = c.original_id
		  and f.client_id= c.client_id
		  and f.create_time &gt;= days_sub(CURRENT_DATE(),2 )
		  and done = 0
    </select>-->

	<select id="findUntreatedDataIds" parameterType="com.voc.service.model.entity.ModLlmReturnResultInfoRecordEntity" >
		with comm_data as (
			select original_id, client_id  from mod_llm_return_result_info_record
			where
				create_time &gt;= days_sub(CURRENT_DATE(),2)
			  and done = 0
			group by original_id,client_id
			limit #{pushNormalizerFlowDatasetSize}
		)
		select new_id from mod_llm_return_result_info_record
		where new_id in (
			select voice_id from mod_merge_status_record f, comm_data c
			where f.original_id = c.original_id
		)
    </select>

</mapper>
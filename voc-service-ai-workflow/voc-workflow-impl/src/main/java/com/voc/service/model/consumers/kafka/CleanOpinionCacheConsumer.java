package com.voc.service.model.consumers.kafka;

import cn.hutool.json.JSONUtil;
import com.voc.service.logs.dto.MessageDTO;
import com.voc.service.model.api.IOpinionAndTagsMappingService;
import com.voc.service.model.config.AIWorkflowConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * @创建者: fanrong
 * @创建时间: 2024/9/10 上午9:27
 * @描述:
 **/
@Component("opinionCacheConsumer.consumer.kafka")
public class CleanOpinionCacheConsumer {
    private static final Logger log = LoggerFactory.getLogger(CleanOpinionCacheConsumer.class);
    @Autowired
    IOpinionAndTagsMappingService mappingService;
    @Autowired
    AIWorkflowConfig config;

    public static final String TOPIC_EVENT = "cleanCache";

    @KafkaListener(topics = {TOPIC_EVENT},groupId = "VDP-cleanOpinion-"+"#{T(java.util.UUID).randomUUID()}")
    public void onMessage(@Payload String message) {
        log.info("clean_opinion_cache message:{}", message);
        MessageDTO bean = JSONUtil.toBean(message, MessageDTO.class);
        if(String.valueOf(bean.getData()).equalsIgnoreCase(Boolean.TRUE.toString())){
            log.info("开始清除本地缓存...");
            mappingService.cleanOpinionCache(config.getTagMappingDb());
            log.info("清除本地缓存完成");
        }
    }
}

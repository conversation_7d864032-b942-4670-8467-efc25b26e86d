package com.voc.service.model.nodes;

import com.voc.service.model.api.IModLlmBatchInfoRecordService;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "modifyAIModelBatchNumberNode", name = "更新本次执行批次号状态节点")
public class ModifyAIModelBatchNumberNode extends AbstractNode {

    private static final Logger log = LoggerFactory.getLogger(ModifyAIModelBatchNumberNode.class);
    @Autowired
    IModLlmBatchInfoRecordService modLlmBatchInfoRecordService;

    @Override
    public void process() throws Exception {
        try {
            ThirdPartyAIContext context = this.getRequestData();
            String batchId = context.getBatchId();
            int done = modLlmBatchInfoRecordService.modifyToDone(batchId);
            log.info("批次状态更新");
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        return true;
    }

}

package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import com.voc.service.common.util.JsonlSplitterUtils;
import com.voc.service.common.util.JsonlUtils;
import com.voc.service.common.util.ServiceContextHolder;
import com.voc.service.components.minio.config.MinioConfig;
import com.voc.service.components.minio.service.UploadFileService;
import com.voc.service.model.api.IModLlmBatchInfoRecordDetailedService;
import com.voc.service.model.api.IModLlmPromptTemplateService;
import com.voc.service.model.entity.ModLlmBatchInfoRecord;
import com.voc.service.model.enums.BatchStatusEnum;
import com.voc.service.model.mapper.ModLlmBatchInfoRecordDetailedMapper;
import com.voc.service.model.mapper.ModLlmBatchInfoRecordMapper;
import com.voc.service.model.model.AiRequerstJsonlModel;
import com.voc.service.model.model.AiRequestDataModel;
import com.voc.service.model.model.ModLlmBatchInfoRecordDetailedModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.voc.service.model.producers.kafka.AnalysisProducer;
import com.voc.service.model.producers.kafka.EventProducer;
import com.voc.service.model.vo.ModLlmPromptTemplateVo;
import com.voc.service.trhird.api.ZhiPuAiApi;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.zhipu.oapi.service.v4.batchs.BatchResponse;
import com.zhipu.oapi.service.v4.file.FileApiResponse;
import com.zhipu.oapi.service.v4.file.UploadFileRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


@LiteflowComponent(id = "aiOffModelZhiPuAiNode", name = "离线智谱模型处理节点")
public class AiOffModelZhiPuAiNode extends AbstractNode {
    private static final Logger log = LoggerFactory.getLogger(AiOffModelZhiPuAiNode.class);
    @Autowired
    EventProducer eventProducer;
    @Autowired
    IModLlmPromptTemplateService promptTemplateService;
    @Autowired
    ModLlmBatchInfoRecordMapper batchInfoRecordMapper;
    @Autowired
    ModLlmBatchInfoRecordDetailedMapper recordDetailedMapper;
    @Autowired
    IModLlmBatchInfoRecordDetailedService batchInfoRecordDetailedService;
    @Autowired
    AnalysisProducer analysisProducer;
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    @Autowired
    UploadFileService uploadFileService;
    @Autowired
    MinioConfig config;
    @Autowired
    ZhiPuAiApi zhipuaiApi;

    @Override
    public void process() throws Exception {
        try {
            ThirdPartyAIContext context = this.getRequestData();
            Map<String, ModLlmPromptTemplateVo> sources = promptTemplateService.getAllPromptTemplate();
            List<AiRequestDataModel> elements = context.getDataList();
            List<CompletableFuture<AiRequerstJsonlModel>> futures = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(elements)) {
                ExecutorService executor = Executors.newWorkStealingPool(); // 使用ForkJoinPool来提高效率
                for (AiRequestDataModel e : elements) {
                    CompletableFuture<AiRequerstJsonlModel> future = CompletableFuture.supplyAsync(() -> {
                        try {
                            return new AiRequerstJsonlModel(e, sources);
                        } catch (Exception ex) {
                            throw new RuntimeException(ex);
                        }
                    }, executor);
                    futures.add(future);
                }

                // 等待所有Future完成
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

                // 当所有Future完成后，构建最终的dataList
                CompletableFuture<List<AiRequerstJsonlModel>> dataListFuture = allFutures.thenApply(v -> {
                    return futures.stream()
                            .map(CompletableFuture::join) // 等待每个Future完成并获取结果
                            .collect(Collectors.toList());
                });

                // 获取最终的dataList
                List<AiRequerstJsonlModel> dataList = dataListFuture.join();
                String fileName = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_FORMATTER) + RandomUtil.randomNumbers(3);
                Path jsonlfile = JsonlUtils.convertListToJsonlFile(dataList, fileName);
                long fileSize = Files.size(jsonlfile);
                String purpose = "batch";
                List<String> customIds = dataList.stream().map(AiRequerstJsonlModel::getCustomId).collect(Collectors.toList());
                if (fileSize > MAX_FILE_SIZE) {//大于100M时
                    Map<List<String>, Path> maps = JsonlSplitterUtils.splitJsonlFile(jsonlfile.toString());
                    for (Map.Entry<List<String>, Path> pathEntry : maps.entrySet()) {
                        extracted(purpose, pathEntry.getValue(), pathEntry.getKey());
                        JsonlUtils.deleteFile(pathEntry.getValue().toFile());
                        log.debug("删除拆分后的文件:{}", pathEntry.getValue().toString());
                    }
                } else {
                    extracted(purpose, jsonlfile, customIds);
                }
                uploadFileService.putObject(getFileName(FileUtil.getName(jsonlfile)), Files.newInputStream(jsonlfile));
                log.debug("删除文件:{}", jsonlfile.toString());
                JsonlUtils.deleteFile(jsonlfile.toFile());
                executor.shutdown(); // 关闭线程池
            }
        } catch (Exception e) {
            log.error("Exception in process: " + e.getMessage(), e);
            throw new Exception(e.getMessage(), e);
        }
    }

    private String getFileName(String name) {
        return ServiceContextHolder.getSystemId().concat("/").concat("zhipuai-upload-file").concat("/").concat(name);
    }


    private void extracted(String purpose, Path jsonlfile, List<String> customIds) {
        UploadFileRequest request = UploadFileRequest.builder()
                .purpose(purpose)
                .filePath(jsonlfile.toString())
                .build();
        ModLlmBatchInfoRecord batchInfoRecord = new ModLlmBatchInfoRecord();
        batchInfoRecord.setBatchSize(customIds.size());
        batchInfoRecord.setInputFileName(FileUtil.getName(jsonlfile));
        batchInfoRecord.setCreateTime(LocalDateTime.now());
        batchInfoRecord.setUpdateTime(LocalDateTime.now());
        FileApiResponse fileApiResponse = null;
        try {
            fileApiResponse = zhipuaiApi.invokeUploadFileApi(request);
        } catch (Exception e) {
            log.error("上传文件失败！：{}", e.getMessage());
            batchInfoRecord.setBatchStatus(BatchStatusEnum.FILE_NOT_UPLOADED.getCode());
            batchInfoRecordMapper.insert(batchInfoRecord);
            e.printStackTrace();
            return;
        }
        if (fileApiResponse.isSuccess()) {
            batchInfoRecord.setInputFileId(fileApiResponse.getData().getId());
            BatchResponse batchResponse = null;
            try {
                batchResponse = zhipuaiApi.batchesCreate(fileApiResponse.getData().getId());
            } catch (Exception e) {
                log.error("创建Batch失败！：{}", e.getMessage());
                batchInfoRecord.setBatchStatus(BatchStatusEnum.BATCH_NOT_CREATED.getCode());
                batchInfoRecordMapper.insert(batchInfoRecord);
                e.printStackTrace();
                return;
            }
            if (batchResponse.isSuccess()) {
                batchInfoRecord.setBatchStatus(BatchStatusEnum.VALIDATING.getCode());
                batchInfoRecord.setBatchId(batchResponse.getData().getId());
                log.debug("创建Batch成功:{}", batchResponse.getData().getId());
            } else {
                batchInfoRecord.setBatchStatus(BatchStatusEnum.BATCH_NOT_CREATED.getCode());
                log.debug("创建Batch失败:{}", jsonlfile.toString());
            }
            log.debug("上传文件成功:{}", jsonlfile.toString());
        } else {
            log.debug("上传文件失败:{}", jsonlfile.toString());
            batchInfoRecord.setBatchStatus(BatchStatusEnum.FILE_NOT_UPLOADED.getCode());
        }
        batchInfoRecordMapper.insert(batchInfoRecord);

        List<ModLlmBatchInfoRecordDetailedModel> batchInfoRecordDetailed = IntStream.range(0, customIds.size())
                .mapToObj(index -> new ModLlmBatchInfoRecordDetailedModel(customIds.get(index), batchInfoRecord.getNewId()))
                .collect(Collectors.toList());
        try {
            analysisProducer.pushBatchContentIds(batchInfoRecordDetailed);
        } catch (Exception e) {
            log.error("kafka producer batch info content record error: {}", e.getMessage());
            e.printStackTrace();
        }
    }
}

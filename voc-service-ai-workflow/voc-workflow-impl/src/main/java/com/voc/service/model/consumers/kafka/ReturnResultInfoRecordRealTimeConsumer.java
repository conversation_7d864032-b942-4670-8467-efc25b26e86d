package com.voc.service.model.consumers.kafka;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.analysis.dto.MessageExt;
import com.voc.service.model.api.IAnalysisService;
import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.api.IModLlmReturnResultInfoRecordService;
import com.voc.service.model.model.MergeStatusRecordModel;
import com.voc.service.model.producers.kafka.ReturnResultInfoRecordProducer;
import com.voc.service.model.producers.kafka.UnprocessedResultDataProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CatListener
 * @createTime 2024年03月14日 9:46
 * @Copyright cuick
 */
@Component("resultInfoRecord.consumer.kafka")
public class ReturnResultInfoRecordRealTimeConsumer {
    public static final String TOPIC_DATA = "modLlmReturnResultInfoRecord_0";

    static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss", Locale.CHINA);
    public static final String CHECK_ORIGINALID = "ai-workflow".concat("::check_originalId::");
    private static final Logger log = LoggerFactory.getLogger(ReturnResultInfoRecordRealTimeConsumer.class);
    @Autowired
    IModLlmReturnResultInfoRecordService iModLlmReturnResultInfoRecordService;
    @Autowired
    KafkaTemplate<String, String> kafkaTemplate;
    @Autowired
    UnprocessedResultDataProducer unprocessedResultDataProducer;
    @Autowired
    IModLlmReturnResultInfoRecordService modLlmReturnResultInfoRecordService;
    @Autowired
    IMergeStatusRecordService mergeStatusRecordService;
    @Autowired
    IAnalysisService analysisService;
    @Autowired
    RedisTemplate redisTemplate;

    @KafkaListener(id = TOPIC_DATA, topics = {TOPIC_DATA, ReturnResultInfoRecordProducer.TOPIC_DATA_RETRY}, groupId = "${model.ai_workflow.llm.customer.group.to_model_topic_group}")
    public void onMessage(String message) {
        log.debug(">>>>>>> 收到 {} {}的请求 <<<<<<<<<<<<<<", TOPIC_DATA, message);
        String originalId = null;
        JSONObject jsonObj = null;
        int retry = 0;
        try {
            if (StrUtil.isBlank(message)) {
//            if (true) {
                log.error("message {}", message);
                return;
            }
//
            jsonObj = JSONUtil.parseObj(message);
            originalId = jsonObj.getStr("originalId");
            final String voiceId = jsonObj.getStr("newId");
            final String retryStr = jsonObj.getStr("retry");
            final String voiceCountStr = jsonObj.getStr("voiceCount");        // 内容包含多少条声音数据
            final String hightLevelStr = jsonObj.getStr("hightLevel");        // 内容包含多少条声音数据
            Assert.isTrue(StrUtil.isNotBlank(originalId), "originalId cannot be empty");
            Assert.isTrue(StrUtil.isNotBlank(voiceId), "voiceCount voiceId be empty");
            Assert.isTrue(StrUtil.isNotBlank(voiceCountStr), "voiceCount cannot be empty");
            boolean hightLevel = false;
            if (StrUtil.isNotBlank(hightLevelStr)) {
                Assert.isTrue(!StrUtil.equalsAnyIgnoreCase(hightLevelStr, "true") || !StrUtil.equalsAnyIgnoreCase(hightLevelStr, "false")
                        , "hightLevelStr cannot be empty");
                hightLevel = BooleanUtil.toBoolean(hightLevelStr);
            }
            retry = NumberUtil.parseInt(Optional.ofNullable(retryStr).orElse("0"));
            final int voiceCount = NumberUtil.parseInt(voiceCountStr);
            log.info("modLlmReturnResultInfoRecord originalId:{}, voiceCount:{}", originalId, voiceCount);
            if (retry >= 60) {
                log.error("重试次数超过60次，跳过 {}", message);
                return;
            }
            Boolean flag = redisTemplate.opsForValue().setIfAbsent(CHECK_ORIGINALID.concat(originalId), LocalDateTime.now().format(formatter), 24, TimeUnit.HOURS);
            // 加锁失败，已有消费端在此时对此消息进行处理，这里不再做处理
            if (Boolean.FALSE.equals(flag)) {
                log.info("originalId {} 正在处理中 ", originalId);
                return;
            }
            //判断数据是否已落库，并且所有对应声音数据是否落库完整
            final Set<String> ids = modLlmReturnResultInfoRecordService.findVoiceId(originalId);
            log.info("modLlmReturnResultInfoRecord ids :{}", ids);
            final boolean isDone = mergeStatusRecordService.isWorkFlowSuccessed(ids, 14);
            //流程是否全部执行成功
            if (isDone) {
                log.info("modLlmReturnResultInfoRecord此数据已执行跳过 voiceId：{} originalId：{}", voiceId, originalId);
                modLlmReturnResultInfoRecordService.modifyToDone(new ArrayList<>(ids));
                redisTemplate.delete(CHECK_ORIGINALID.concat(originalId));
                return;
            }
            log.info("modLlmReturnResultInfoRecord开始处理请求 :{}", originalId);
            List<MergeStatusRecordModel> checkMergeData = mergeStatusRecordService.getModelListCahce(ids);
            if (CollUtil.isNotEmpty(ids) && ids.size() >= voiceCount && checkMergeData.size() == ids.size()) {
                //发送处理数据到mq
                log.info("modLlmReturnResultInfoRecord推送 {} mq数据:{}", ReturnResultInfoRecordProducer.TOPIC_DATA_RETRY, ids);

                //根据 hightLevel 值，分发到不同的topic
                unprocessedResultDataProducer.pushData(MessageDTO.builder().data(ids)
                        .ext(Set.of(
                                MessageExt.builder().key("message").value(message).build(),
                                MessageExt.builder().key("originalId").value(originalId).build(),
                                MessageExt.builder().key("hightLevel").value(hightLevel).build()
                        ))
                        .build());
            } else {
                try {
                    log.info("modLlmReturnResultInfoRecord重新放入 {} mq数据:{}", ReturnResultInfoRecordProducer.TOPIC_DATA_RETRY, jsonObj);
                    Thread.sleep(1000);
                    jsonObj.putOpt("retry", retry + 1);
                    redisTemplate.delete(CHECK_ORIGINALID.concat(originalId));
                    kafkaTemplate.send(ReturnResultInfoRecordProducer.TOPIC_DATA_RETRY, JSONUtil.toJsonStr(jsonObj));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    redisTemplate.delete(CHECK_ORIGINALID.concat(originalId));
                }
            }
            log.info("modLlmReturnResultInfoRecord消费结束：{}", originalId);
        } catch (Exception e) {
            log.info("异常重新放入mq数据:{}", jsonObj);
            redisTemplate.delete(CHECK_ORIGINALID.concat(originalId));
            jsonObj.putOpt("retry", retry + 1);
            kafkaTemplate.send(ReturnResultInfoRecordProducer.TOPIC_DATA_RETRY, JSONUtil.toJsonStr(jsonObj));
            log.error(e.getMessage(), e);
        }
    }


}


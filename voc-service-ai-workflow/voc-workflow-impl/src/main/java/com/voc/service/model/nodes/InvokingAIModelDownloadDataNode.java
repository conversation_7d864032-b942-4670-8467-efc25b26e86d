package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.voc.service.common.util.IdWorker;
import com.voc.service.model.api.IModLlmBatchInfoRecordService;
import com.voc.service.model.api.IModLlmReturnResultExceptionRecordService;
import com.voc.service.model.model.ModLlmReturnResultExceptionRecordModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.voc.service.trhird.api.ZhiPuAiApi;
import com.voc.service.trhird.model.ZhiPuAiContentModel;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "invokingAIModelDownloadDataNode", name = "根据批次号调用模型获取执行结果节点")
public class InvokingAIModelDownloadDataNode extends AbstractNode {

    private static final Logger log = LoggerFactory.getLogger(InvokingAIModelDownloadDataNode.class);
    @Autowired
    private ZhiPuAiApi zhiPuAiApi;

    @Autowired
    IModLlmBatchInfoRecordService modLlmBatchInfoRecordService;

    @Autowired
    IModLlmReturnResultExceptionRecordService iModLlmReturnResultExceptionRecordService;

    @Override
    public void process() throws Exception {
        try {
            ThirdPartyAIContext context = this.getRequestData();
            String outFileId = this.getPrivateDeliveryData();
            Assert.isTrue(StringUtils.isNotEmpty(outFileId), "outFileIdList cannot be empty");
            Pair<List<ZhiPuAiContentModel>, List<String>> zhiPuAiContentModelList = zhiPuAiApi.downloadFileData(outFileId);
            log.info("调用智谱下载文件接口完成");
            if (ObjectUtils.isEmpty(zhiPuAiContentModelList)) {
                log.error("调用智谱下载文件接口返回为空");
            }
            if (CollectionUtil.isNotEmpty(zhiPuAiContentModelList.getLeft())) {
                context.setZhiPuAiContentModelList(zhiPuAiContentModelList.getLeft());
            }
            if (CollectionUtil.isNotEmpty(zhiPuAiContentModelList.getRight())) {
                List<String> exceptionData = zhiPuAiContentModelList.getRight();
                log.info("智谱异常数据数量:{}", exceptionData.size());
                List<ModLlmReturnResultExceptionRecordModel> recordModelList = new ArrayList<>();
                for (String errorData : exceptionData) {
                    ModLlmReturnResultExceptionRecordModel recordModel = new ModLlmReturnResultExceptionRecordModel();
                    recordModel.setData(errorData);
                    recordModel.setNewId(IdWorker.getId());
                    recordModel.setBatchId(context.getBatchId());
                    recordModelList.add(recordModel);
                }
                log.info("智谱返回批次数量:{}", recordModelList.size());
                iModLlmReturnResultExceptionRecordService.saveReturnResultExceptionData(recordModelList);
            }
            if (StrUtil.isBlankIfStr(context.getZhiPuStatusAiModel().getErrorFileId())) {
                zhiPuAiApi.batchFileDelete(context.getZhiPuStatusAiModel().getInputFileId());
            }
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        return true;
    }


}

package com.voc.service.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @创建者: fanrong
 * @创建时间: 2024/8/6 下午2:42
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "mod_car_and_dim_opinion_mapping")
public class CarAndDimOpinionMappingEntity implements Serializable {
    /**
     * 整车体系
     */
    private String car;
    /**
     * 评价维度
     */
    private String dim;
    private String opinion;
    private String topic;

}

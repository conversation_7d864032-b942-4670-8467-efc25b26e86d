package com.voc.service.model.nodes;

import cn.hutool.core.util.StrUtil;
import com.voc.service.model.config.AIWorkflowConfig;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ModelContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "parseCarAndDimSchemaNode", name = "拼装schema数据（整车+评价）节点")
public class ParseCarAndDimSchemaNode extends AbstractNode {
    private static final Logger log = LoggerFactory.getLogger(ParseCarAndDimSchemaNode.class);
    @Autowired
    AIWorkflowConfig config;

    @Override
    public void process() throws Exception {
        try {
            ModelContext context = this.getRequestData();
            log.info("tag {}  getTagMappingDb {}", context.getTag(), config.getTagMappingDb());

            final String clientId = config.getTagMappingDb();


        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        ModelContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getTag()), "getTag cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(config.getTagMappingDb()), "clientId cannot be empty");
//        Assert.isTrue(StrUtil.isNotBlank(context.getClientId()), "getClientId cannot be empty");
//        Assert.isTrue(CollUtil.isNotEmpty(context.getIds()), "getIds cannot be empty");


        return true;
    }

}

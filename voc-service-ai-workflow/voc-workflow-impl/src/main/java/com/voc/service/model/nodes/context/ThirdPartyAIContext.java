package com.voc.service.model.nodes.context;

import com.voc.service.model.model.AiRequestDataModel;
import com.voc.service.model.model.MergeStatusRecordModel;
import com.voc.service.model.nodes.abstracts.AbstractContext;
import com.voc.service.trhird.model.ZhiPuAiContentModel;
import com.voc.service.trhird.model.ZhiPuStatusAiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * @Title: AiWorkflowDefaultContext
 * @Package: com.voc.service.model.nodes.context
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 14:28
 * @Version:1.0
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
public class ThirdPartyAIContext extends AbstractContext implements Serializable {


    @Schema(description = "批次号")
    String batchId;
    String workId;
    @Schema(description = "渠道Id")
    String channelId;
    @Schema(description = "文章类型")
    String contentType;
    @Schema(description = "模型类型")
    Integer modelType;
    @Schema(description = "客户ID")
    String clientId;
    String prompt;
    String apiToken;
    @Schema(description = "智谱AI状态接口返回数据")
    ZhiPuStatusAiModel zhiPuStatusAiModel;
    @Schema(description = "智谱AI下载接口返回数据")
    List<ZhiPuAiContentModel> zhiPuAiContentModelList;
    @Schema(description = "初始化监控状态数据")
    List<MergeStatusRecordModel> mergeStatusRecordModelList;

    List<AiRequestDataModel> dataList;
    AiRequestDataModel dataModel;
}

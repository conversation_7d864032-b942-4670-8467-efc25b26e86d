package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.voc.service.model.api.IAnalysisService;
import com.voc.service.model.api.IModRequestAiStatusService;
import com.voc.service.model.model.ModLlmReturnResultInfoRecordModel;
import com.voc.service.model.model.ModWorkflowModel;
import com.voc.service.model.model.VoiceClipRequestDataModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.voc.service.model.nodes.context.ModelContext;
import com.voc.service.model.producers.kafka.MergingResultDataProducer;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.*;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "invokingRawNormalizerNode", name = "调用模型评价服务节点")
public class InvokingRawNormalizerNode extends AbstractNode {
    private static final Logger log = LoggerFactory.getLogger(InvokingRawNormalizerNode.class);
    @Autowired
    MergingResultDataProducer mergingResultDataProducer;
    @Resource
    private FlowExecutor flowExecutor;

    @Autowired
    IAnalysisService iAnalysisService;

    @Autowired
    IModRequestAiStatusService iModRequestAiStatusService;

    static final String chinnelId = "raw_normalizer_flow_v2";

    @Override
    public void process() throws Exception {
        AIResultDataContext context = this.getRequestData();
        try {
            log.info("tag {}", this.getTag());
            final List<ModLlmReturnResultInfoRecordModel> aiLabelModelList;

            if ("scenario".equals(this.getTag())) {
                aiLabelModelList = context.getScenarioDataList();
            } else if ("opinion".equals(this.getTag())) {
                aiLabelModelList = context.getOpinionDataList();
            } else {
                throw new Exception("tag is error");
            }

            if (CollUtil.isEmpty(aiLabelModelList)) {
                log.debug("无数据，跳过本次节点");
                return;
            }

            if (!Arrays.asList("scenario", "opinion").contains(this.getTag())) {
                throw new Exception("tag is error");
            }
            Map<Integer, List<ModWorkflowModel>> maps = new HashMap<>();
            context.getStopWatch().start("执行 raw_normalizer_flow_v2 整车标签服务");


            for (ModLlmReturnResultInfoRecordModel aiLabelModel : aiLabelModelList) {
                ModelContext modelContext = ModelContext.builder().build();
                modelContext.setTag(this.getTag());
                VoiceClipRequestDataModel build = VoiceClipRequestDataModel.builder().contentId(aiLabelModel.getOriginalId())
                        .new_id(aiLabelModel.getNewId())
                        .subject(aiLabelModel.getSubject()).tag(this.getTag())
                        .scenario(aiLabelModel.getScenario())
                        .aspect(aiLabelModel.getAspect())
                        .sentiment(aiLabelModel.getSentiment())
                        .intent(aiLabelModel.getIntent())
                        .desc(aiLabelModel.getDescription()).build();
                modelContext.setInputDataset(build);
                modelContext.setClientId(aiLabelModel.getClientId());
                modelContext.setDataId(aiLabelModel.getNewId());
                modelContext.setContentId(aiLabelModel.getOriginalId());
                modelContext.setChannelId(aiLabelModel.getChannelId());
                modelContext.setModelType(aiLabelModel.getModelType());
                modelContext.setContentType(aiLabelModel.getContentType());
                modelContext.setWorkId(context.getWorkId());
                modelContext.getStopWatch().start("执行 raw_normalizer_flow_v2 -> dataId=".concat(aiLabelModel.getNewId()));
                try {
                    LiteflowResponse response = flowExecutor.execute2Resp(chinnelId, modelContext,context.getWorkId());
                    if (!response.isSuccess()) {
                        log.error("workId:{}  {}", modelContext.getWorkId(), response.getCause());
                        throw response.getCause();
                    }
                }catch (Exception e){
                    log.error("调用模型评价服务节点异常:",e);
                }finally {
                    log.info("workId {} 完成 ", context.getWorkId());
//                    modelContext.getStopWatch().prettyPrint();
                }
                if ("opinion".equals(this.getTag())) {
                    if (maps.containsKey(4)) {
                        maps.get(4).add(ModWorkflowModel.builder().voiceId(aiLabelModel.getNewId()).originalId(aiLabelModel.getOriginalId()).build());
                    } else {
                        maps.put(4, new ArrayList<>(Arrays.asList(
                                ModWorkflowModel.builder().voiceId(aiLabelModel.getNewId()).originalId(aiLabelModel.getOriginalId()).build())
                        ));
                    }

                } else if ("scenario".equals(this.getTag())) {
                    if (maps.containsKey(8)) {
                        maps.get(8).add(ModWorkflowModel.builder().voiceId(aiLabelModel.getNewId()).originalId(aiLabelModel.getOriginalId()).build());
                    } else {
                        maps.put(8, new ArrayList<>(Arrays.asList(
                                ModWorkflowModel.builder().voiceId(aiLabelModel.getNewId()).originalId(aiLabelModel.getOriginalId()).build())
                        ));
                    }
                }
            }
            context.getStopWatch().stop();
            context.getStopWatch().start("整车标签服务调用记录添加db");

            if (CollUtil.isNotEmpty(maps)) {
                this.sendPrivateDeliveryData("modifyResultDataStatusNode", maps);
            } else {
                log.error("modifyResultDataStatusNode 入参异常");
            }
            context.getStopWatch().stop();
        } catch (Exception e) {
            log.error("调用模型评价服务节点异常");
            throw new Exception(e.getMessage(), e);
        } finally {
            log.info("workId {} 完成 ", context.getWorkId());
//            context.getStopWatch().prettyPrint();
        }
    }

    @Override
    public boolean isAccess() {
        AIResultDataContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(this.getTag()), "getClientId cannot be empty");
        return true;
    }

}

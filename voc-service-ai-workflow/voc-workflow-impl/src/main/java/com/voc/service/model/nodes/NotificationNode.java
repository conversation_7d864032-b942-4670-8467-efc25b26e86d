package com.voc.service.model.nodes;

import cn.hutool.core.util.StrUtil;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.producers.kafka.EventProducer;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.FlowExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "notificationNode", name = "事件通知节点")
public class NotificationNode extends AbstractNode {
    private static final Logger log = LoggerFactory.getLogger(NotificationNode.class);
    @Autowired
    FlowExecutor flowExecutor;
    @Autowired
    EventProducer eventProducer;

    @Override
    public void process() throws Exception {
        try {
            log.info("event [{}]", this.getTag());

            eventProducer.pushEvent(MessageDTO.builder().type(this.getTag()).build());
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        Assert.isTrue(StrUtil.isNotBlank(this.getTag()), "getTag cannot be empty");

        return true;
    }

}

package com.voc.service.model.nodes;


import cn.hutool.core.collection.CollUtil;
import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.model.ModLlmReturnResultInfoRecordModel;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "filteredRequiredFieldsDatasetDataNode", name = "是否有异常数据集节点")
public class FilteredRequiredFieldsDatasetDataNode extends AbstractNodeIf {
    private static final Logger log = LoggerFactory.getLogger(FilteredRequiredFieldsDatasetDataNode.class);
    @Autowired
    IMergeStatusRecordService mergeStatusRecordService;

    @Override
    public boolean processIf() throws Exception {
        try {
            AIResultDataContext context = this.getRequestData();
            Map<String, List<ModLlmReturnResultInfoRecordModel>> map = new HashMap<>();
            try {
//                if ("opinion".equalsIgnoreCase(this.getTag())) {
                if (CollUtil.isNotEmpty(context.getOpinionDataList())) {
                    //sub + desc 字段不能为空
                    final List<ModLlmReturnResultInfoRecordModel> filteringCompleted = context.getOpinionDataList().stream()
                            .filter(f -> (StringUtils.isBlank(f.getSubject()) || "NA".equals(f.getSubject()) || "na".equals(f.getSubject()))
                                    && (StringUtils.isBlank(f.getDescription()) || "NA".equals(f.getDescription()) || "na".equals(f.getDescription())))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(filteringCompleted)) {
                        map.put("opinion", filteringCompleted);
                    }

                    log.info("过滤前：{}， 过滤掉：{}", context.getOpinionDataList().size(), filteringCompleted.size());
                    final Set<String> ids = filteringCompleted.stream().map(ModLlmReturnResultInfoRecordModel::getNewId).collect(Collectors.toSet());
                    //后端需要执行得数据集
                    context.setOpinionDataList(context.getOpinionDataList().stream().filter(m -> !ids.contains(m.getNewId())).collect(Collectors.toList()));


                }
//                if ("scenario".equalsIgnoreCase(this.getTag())) {
                if (CollUtil.isNotEmpty(context.getScenarioDataList())) {
                    //Scenario 字段不能为空
                    final List<ModLlmReturnResultInfoRecordModel> filteringCompleted = context.getScenarioDataList().stream()
                            .filter(f -> (StringUtils.isBlank(f.getScenario()) || "NA".equals(f.getScenario()) || "na".equals(f.getScenario())))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(filteringCompleted)) {
                        map.put("scenario", filteringCompleted);
                    }

                    log.info("过滤前：{}， 过滤掉：{}", context.getScenarioDataList().size(), filteringCompleted.size());
                    final Set<String> ids = filteringCompleted.stream().map(ModLlmReturnResultInfoRecordModel::getNewId).collect(Collectors.toSet());
                    //后端需要执行得数据集
                    context.setScenarioDataList(context.getScenarioDataList().stream().filter(m -> !ids.contains(m.getNewId())).collect(Collectors.toList()));


                }
//                if ("brand".equalsIgnoreCase(this.getTag())) {
                if (CollUtil.isNotEmpty(context.getBrandCarDataList())) {
                    //sub + desc 字段不能为空
                    final List<ModLlmReturnResultInfoRecordModel> filteringCompleted = context.getBrandCarDataList().stream()
                            .filter(f -> (StringUtils.isBlank(f.getSubject()) || "NA".equals(f.getSubject()) || "na".equals(f.getSubject()))
                                    && (StringUtils.isBlank(f.getDescription()) || "NA".equals(f.getDescription()) || "na".equals(f.getDescription())))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(filteringCompleted)) {
                        map.put("brand", filteringCompleted);
                    }

                    log.info("过滤前：{}， 过滤掉：{}", context.getBrandCarDataList().size(), filteringCompleted.size());
                    final Set<String> ids = filteringCompleted.stream().map(ModLlmReturnResultInfoRecordModel::getNewId).collect(Collectors.toSet());
                    //后端需要执行得数据集
                    context.setBrandCarDataList(context.getBrandCarDataList().stream().filter(m -> !ids.contains(m.getNewId())).collect(Collectors.toList()));


                } else ;
            } finally {
                log.info("执行是否有异常数据集节点:{}",map.size());
                if (CollUtil.isNotEmpty(map)) {
                    context.setFilteredRequiredFieldsMap(map);
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }


    @Override
    public boolean isAccess() {
        return true;
    }

}

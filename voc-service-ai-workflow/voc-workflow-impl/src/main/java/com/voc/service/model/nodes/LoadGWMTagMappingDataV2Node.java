package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.voc.service.model.api.ICarAndDimChannelMappingService;
import com.voc.service.model.api.IOpinionAndTagsMappingService;
import com.voc.service.model.api.IScenariosTagsLevelMappingService;
import com.voc.service.model.config.AIWorkflowConfig;
import com.voc.service.model.model.OpinionAndTagsMappingModel;
import com.voc.service.model.model.VoiceClipResultData;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.ModelContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "loadGWMTagMappingDataV2Node", name = "接口获取标签映射数据（长城）节点")
public class LoadGWMTagMappingDataV2Node extends AbstractNodeIf {
    private static final Logger log = LoggerFactory.getLogger(LoadGWMTagMappingDataV2Node.class);
    @Autowired
    AIWorkflowConfig config;
    @Autowired
    ICarAndDimChannelMappingService carAndDimChannelMappingService;
    @Autowired
    IScenariosTagsLevelMappingService carAndDimOpinionMappingService;
    @Autowired
    IOpinionAndTagsMappingService viewpointAndTagsMappingService;

    @Override
    public boolean processIf() throws Exception {
        try {
            ModelContext context = this.getRequestData();
            final String clientId = config.getTagMappingDb();
            List<VoiceClipResultData> processingLabeledDataset = context.getProcessingLabeledDataset();
            VoiceClipResultData voiceClipResultData = processingLabeledDataset.stream().findFirst().get();

            {
                //观点
                Assert.isTrue(StrUtil.isNotBlank(voiceClipResultData.getSim_opinion()), "voiceClipResultData.getSim_opinion() cannot be empty");
                final String opinion = voiceClipResultData.getSim_opinion();
                //获取观点与标签的映射
                final List<OpinionAndTagsMappingModel> modelList = viewpointAndTagsMappingService.getModelList(clientId, opinion);
                if (CollUtil.isEmpty(modelList)) {
                    log.info("【观点:{}】未匹配到对应的标签映射数据集", opinion);
                    context.setProcessingUnLabeledDataset(processingLabeledDataset);
                    context.setProcessingLabeledDataset(Collections.EMPTY_LIST);
                    context.setNodeIdentifier("loadTagMappingData");
                    return false;
                } else {
                    log.info("获取opinion tag 结果数据集");
                }
                List<VoiceClipResultData> collect = new ArrayList<>();
                //标签去重
                /**
                 * 去重逻辑: 业务标签和质量标签分开去重,最终数据组装时，数据的总数量=业务标签去重后的数量+质量标签去重后的数量
                 * 例: 三条数据
                 * 1. 两条数据业务标签相同，则业务标签去重后只存在2条数据；
                 * 2. 三条数据质量标签相同，则质量标签去重后只存在1条数据；
                 * 3. 最终进行数据组装时，会组出来三条不同的数据，分别为:两条只存在业务标签的数据和一条只存在质量标签的数据
                 */
                //业务标签去重
                Map<String, OpinionAndTagsMappingModel> biz = modelList.stream().filter(e->ObjectUtils.isNotEmpty(e.getBusinessTag())).collect(Collectors.toMap(OpinionAndTagsMappingModel::getBusinessTag, e -> e, (v1, v2) -> v2));
                log.trace("去重后业务标签数据集:[{}]", biz);
                if(ObjectUtils.isNotEmpty(biz)){
                    //去重后的业务标签数据组装
                    List<VoiceClipResultData> collect1 = biz.entrySet().stream().map(e -> {
                        OpinionAndTagsMappingModel value = e.getValue();
                        VoiceClipResultData build = VoiceClipResultData.builder()
                                .id(voiceClipResultData.getId())
                                .businessTagCode(ObjectUtils.isNotEmpty(value.getBusinessTagCode()) ? value.getBusinessTagCode() : null)
                                .businessTagName(ObjectUtils.isNotEmpty(value.getBusinessTag()) ? value.getBusinessTag() : null)
                                .md5(value.getMd5())
                                .score(voiceClipResultData.getScore())
                                .sim_opinion(opinion)
                                .build();
                        return build;
                    }).collect(Collectors.toList());
                    //添加去重后的业务标签数据
                    collect.addAll(collect1);
                }

                //质量标签去重
                Map<String, OpinionAndTagsMappingModel> quality = modelList.stream().filter(e->ObjectUtils.isNotEmpty(e.getQualityTag())).collect(Collectors.toMap(OpinionAndTagsMappingModel::getQualityTag, e -> e, (v1, v2) -> v2));
                log.trace("去重后质量标签数据集:[{}]", quality);
                if(ObjectUtils.isNotEmpty(quality)){
                    //去重后的质量标签数据组装
                    List<VoiceClipResultData> collect2 = quality.entrySet().stream().map(e -> {
                        OpinionAndTagsMappingModel value = e.getValue();
                        VoiceClipResultData build = VoiceClipResultData.builder()
                                .id(voiceClipResultData.getId())
                                .qualityTagCode(ObjectUtils.isNotEmpty(value.getQualityTagCode()) ? value.getQualityTagCode() : null)
                                .qualityTagName(ObjectUtils.isNotEmpty(value.getQualityTag()) ? value.getQualityTag() : null)
                                .md5(value.getMd5())
                                .score(voiceClipResultData.getScore())
                                .sim_opinion(opinion)
                                .build();
                        return build;
                    }).collect(Collectors.toList());
                    //添加去重后的质量标签数据
                    collect.addAll(collect2);
                }

                log.debug("合并后数据集:[{}]", collect);
                context.setProcessingUnLabeledDataset(Collections.EMPTY_LIST);
                context.setProcessingLabeledDataset(collect);
                log.info("opinion collect {}", collect.size());
            }

        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
        return true;
    }

    @Override
    public boolean isAccess() {
        ModelContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getTag()), "getTag cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(config.getTagMappingDb()), "clientId cannot be empty");
//        Assert.isTrue(ObjectUtils.isNotEmpty(context.getProcessingLabeledDataset()), "processingLabeledDataset cannot be empty");
//        Assert.isTrue(StrUtil.isNotBlank(context.getClientId()), "getClientId cannot be empty");
//        Assert.isTrue(CollUtil.isNotEmpty(context.getIds()), "getIds cannot be empty");


        return true;
    }

}

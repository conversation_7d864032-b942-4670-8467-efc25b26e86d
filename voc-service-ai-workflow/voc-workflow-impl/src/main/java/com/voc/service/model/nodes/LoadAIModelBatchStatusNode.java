package com.voc.service.model.nodes;

import com.voc.service.model.api.IModLlmBatchInfoRecordService;
import com.voc.service.model.config.AIWorkflowConfig;
import com.voc.service.model.enums.BatchStatusEnum;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * @Title: Test
 * @Package: com.voc.service.model.nodes
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 15:04
 * @Version:1.0
 */
@LiteflowComponent(id = "loadAIModelBatchStatusNode", name = "加载本次需处理的AI模型已完成状态节点")
public class LoadAIModelBatchStatusNode extends AbstractNodeIf {
    private static final Logger log = LoggerFactory.getLogger(LoadAIModelBatchStatusNode.class);
    @Autowired
    AIWorkflowConfig config;

    @Autowired
    IModLlmBatchInfoRecordService modLlmBatchInfoRecordService;


    @Override
    public boolean processIf() throws Exception {
        log.info("开始处理AI模型已完成batch下载");
        ThirdPartyAIContext context = this.getRequestData();
        String batchId = context.getBatchId();
        List<String> statusList = Collections.singletonList(BatchStatusEnum.COMPLETED.getCode());
        String outFileId = modLlmBatchInfoRecordService.findOutFileId(statusList, batchId);
        if (StringUtils.isEmpty(outFileId)) {
            log.info("没有已完成AI模型batch");
            return Boolean.FALSE;
        }
        log.info("需要下载的AI模型batch:{}", outFileId);
        if (StringUtils.isNoneBlank(outFileId)) {
            this.sendPrivateDeliveryData("invokingAIModelDownloadDataNode", outFileId);
        }
        return true;
    }
}

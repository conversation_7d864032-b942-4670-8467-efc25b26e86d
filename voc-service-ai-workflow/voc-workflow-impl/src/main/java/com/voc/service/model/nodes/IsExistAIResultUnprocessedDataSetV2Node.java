package com.voc.service.model.nodes;


import cn.hutool.core.collection.CollUtil;
import com.voc.service.model.api.IAnalysisService;
import com.voc.service.model.api.IModLlmReturnResultInfoRecordService;
import com.voc.service.model.config.AIWorkflowConfig;
import com.voc.service.model.model.ModLlmReturnResultInfoRecordModel;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryException;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "isExistAIResultUnprocessedDataSetV2Node", name = "判断是否有需要执行的数据ID集合节点")
public class IsExistAIResultUnprocessedDataSetV2Node extends AbstractNodeIf {

    private static final Logger log = LoggerFactory.getLogger(IsExistAIResultUnprocessedDataSetV2Node.class);
    @Autowired
    IAnalysisService iAnalysisService;
    @Autowired
    IModLlmReturnResultInfoRecordService iModLlmReturnResultInfoRecordService;
    @Autowired
    AIWorkflowConfig config;


    @Override
    public boolean processIf() throws Exception {
        try {
            AIResultDataContext context = this.getRequestData();

            final Set<String> allIds = context.getIds();
            log.info("allIds {}", allIds);
            if (CollUtil.isNotEmpty(allIds)) {
                final List<ModLlmReturnResultInfoRecordModel> modelList
                        = iModLlmReturnResultInfoRecordService.findByIds(allIds);
                if (CollUtil.isEmpty(modelList)) {
                    log.info("数据已经处理过直接跳出");
                    return false;
                }
                this.sendPrivateDeliveryData("loadAIResultUnprocessedDataNode", new HashSet<>(allIds));
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }

}

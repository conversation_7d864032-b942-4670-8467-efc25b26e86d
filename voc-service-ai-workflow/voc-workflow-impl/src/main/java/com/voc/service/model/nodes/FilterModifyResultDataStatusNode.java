package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollUtil;
import com.voc.service.model.api.IAnalysisService;
import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.api.IModLlmReturnResultInfoRecordService;
import com.voc.service.model.model.MergeStatusRecordModel;
import com.voc.service.model.model.ModWorkflowModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "filterModifyResultDataStatusNode", name = "过滤数据推送完成状态节点")
public class FilterModifyResultDataStatusNode extends AbstractNode {

    private static final Logger log = LoggerFactory.getLogger(FilterModifyResultDataStatusNode.class);
    @Autowired
    IAnalysisService analysisService;
    @Autowired
    IModLlmReturnResultInfoRecordService modLlmReturnResultInfoRecordService;
    @Autowired
    IMergeStatusRecordService mergeStatusRecordService;

    @Override
    public void process() throws Exception {
        AIResultDataContext context = this.getRequestData();
        try {
            final Map<Integer, List<ModWorkflowModel>> map = context.getFilterMap();
            if (CollUtil.isEmpty(map)) {
                log.error("filterModifyResultDataStatusNode接收参数为空，跳过执行！");
                return;
            }
            //触发修改状态事件监控：  查询数据的修改状态，完成修改后删除redis key
            map.keySet().forEach(status -> {
                try {
                    if (2 == status) {
                        List<ModWorkflowModel> modelList = map.get(status);
                        final Set<String> ids = modelList.stream().map(ModWorkflowModel::getVoiceId).collect(Collectors.toSet());
                        mergeStatusRecordService.updateBrandDataSize(ids, 0);
                    } else if (4 == status) {
                        List<ModWorkflowModel> modelList = map.get(status);
                        final Set<String> ids = modelList.stream().map(ModWorkflowModel::getVoiceId).collect(Collectors.toSet());
                        mergeStatusRecordService.updateLabeledDataSize(ids, 0);
                    } else if (8 == status) {
                        List<ModWorkflowModel> modelList = map.get(status);
                        final Set<String> ids = modelList.stream().map(ModWorkflowModel::getVoiceId).collect(Collectors.toSet());
                        mergeStatusRecordService.updateScenariosDataSize(ids, 0);
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
            //记录已完成所有流程的id集合
            Set<String> doneIdsList = new HashSet<>();
            //修改redis 状态， 返回满足条件的数据id
            map.keySet().forEach(key -> {
                final Set<String> ids = map.get(key).stream().map(ModWorkflowModel::getVoiceId).collect(Collectors.toSet());
                doneIdsList.addAll(ids);
            });
            List<MergeStatusRecordModel> modelListCahce = mergeStatusRecordService.getModelListCahce(doneIdsList);
            List<MergeStatusRecordModel> mergeStatusRecordModels = modelListCahce.stream().
                    filter(entity -> entity.getLabelNum() != -1 && entity.getScenarioNum() != -1 && entity.getBrandCarNum() != -1).toList();
            if (CollUtil.isNotEmpty(mergeStatusRecordModels)) {
                log.info("满足条件，则修改声音数据状态，并删除对应redis key {}", doneIdsList);
                //满足条件，则修改声音数据状态，并删除对应redis key
                List<String> ids = mergeStatusRecordModels.stream().map(MergeStatusRecordModel::getNewId).collect(Collectors.toList());
                modLlmReturnResultInfoRecordService.modifyToDone(ids);
            }

        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        AIResultDataContext context = this.getRequestData();

        return true;
    }

}

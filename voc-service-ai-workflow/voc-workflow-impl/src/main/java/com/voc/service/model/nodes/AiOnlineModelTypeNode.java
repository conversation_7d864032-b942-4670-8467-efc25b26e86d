package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.voc.service.common.util.IdWorker;
import com.voc.service.components.minio.config.MinioConfig;
import com.voc.service.components.minio.service.UploadFileService;
import com.voc.service.model.api.IModLlmBatchInfoRecordDetailedService;
import com.voc.service.model.api.IModLlmPromptTemplateService;
import com.voc.service.model.api.IModLlmReturnResultExceptionRecordService;
import com.voc.service.model.api.IModMetaDataAnalysisService;
import com.voc.service.model.entity.ModMetaDataAnalysisEntity;
import com.voc.service.model.mapper.ModLlmBatchInfoRecordDetailedMapper;
import com.voc.service.model.mapper.ModLlmBatchInfoRecordMapper;
import com.voc.service.model.mapper.ModMetaDataAnalysisMapper;
import com.voc.service.model.model.ModLlmReturnResultExceptionRecordModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.voc.service.model.producers.kafka.AnalysisProducer;
import com.voc.service.model.producers.kafka.EventProducer;
import com.voc.service.trhird.api.CompoundLlmAiApi;
import com.voc.service.trhird.api.ZhiPuAiApi;
import com.voc.service.trhird.model.AIRequestModel;
import com.voc.service.trhird.model.ZhiPuAiContentModel;
import com.voc.service.trhird.vo.AIResponseVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jackson.JsonComponentModule;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "aiOnlineModelTypeNode", name = "实时模型处理节点")
public class AiOnlineModelTypeNode extends AbstractNode {
    private static final Logger log = LoggerFactory.getLogger(AiOnlineModelTypeNode.class);
    @Autowired
    EventProducer eventProducer;
    @Autowired
    IModLlmPromptTemplateService promptTemplateService;
    @Autowired
    ModLlmBatchInfoRecordMapper batchInfoRecordMapper;
    @Autowired
    ModLlmBatchInfoRecordDetailedMapper recordDetailedMapper;
    @Autowired
    IModLlmBatchInfoRecordDetailedService batchInfoRecordDetailedService;
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    @Autowired
    UploadFileService uploadFileService;
    @Autowired
    MinioConfig config;
    @Autowired
    ZhiPuAiApi zhipuaiApi;
    @Autowired
    CompoundLlmAiApi compoundLlmAiApi;
    @Value("${third.compound.llm.modelName}")
    private String modelName;
    @Autowired
    private JsonComponentModule jsonComponentModule;
    @Autowired
    IModMetaDataAnalysisService modMetaDataAnalysisService;
    @Autowired
    IModLlmReturnResultExceptionRecordService iModLlmReturnResultExceptionRecordService;
    @Autowired
    AnalysisProducer analysisProducer;
    @Autowired
    ModMetaDataAnalysisMapper modMetaDataAnalysisMapper;
    @Override
    public void process() throws Exception {
        ThirdPartyAIContext context = this.getRequestData();
        AIRequestModel requestModel = new AIRequestModel();
        requestModel.setModel_name(modelName);
        requestModel.setText(context.getDataModel().getContent());
        requestModel.setUnique_id(context.getDataModel().getId());
        AIResponseVo responseVo = null;
        long startTime = System.currentTimeMillis(); // 开始时间
        log.info("开始执行实时模型处理节点文章id：{}",requestModel.getUnique_id());
        ZhiPuAiContentModel zhiPuAiContentModel=new ZhiPuAiContentModel();
        List<ZhiPuAiContentModel> zhiPuAiContentModelList=new ArrayList<>();
        try {
            responseVo = compoundLlmAiApi.generateAiResponse(requestModel);
        } catch (Exception e) {
            log.error("实时模型处理节点处理失败第二次重试！：{}", e.getMessage());
            try {
                responseVo = compoundLlmAiApi.generateAiResponse(requestModel);
            } catch (Exception ex) {
                log.error("实时模型处理节点第二次重试处理失败！：{}", e.getMessage());
                //更新失败状态
                zhiPuAiContentModel.setCustomId(requestModel.getUnique_id());
                zhiPuAiContentModelList.add(zhiPuAiContentModel);
                List<String> customIds = new ArrayList<String>();
                customIds.add(context.getDataModel().getId());
                modMetaDataAnalysisService.batchUpdateStatus(customIds, 3);
                e.printStackTrace();
            }
        } finally {
            long endTime = System.currentTimeMillis(); // 结束时间
            long durationInSeconds = (endTime - startTime) / 1000; // 计算耗时并转换为秒
            log.info("实时模型处理耗时：{}秒", durationInSeconds); // 记录耗时
        }
        log.info("开始执行实时模型处理节点_模型处理结果文章id：{}",responseVo.getUniqueId());
     /*   long count=modMetaDataAnalysisService.count(context.getDataModel().getId());
        if (count<=0){
            extracted(context);
//            return;
        }*/
        List<ModLlmReturnResultExceptionRecordModel> recordModelList = new ArrayList<>();
        Gson gson = new Gson();
        if (responseVo != null && responseVo.getStatus().equals("success")) {
            zhiPuAiContentModel.setCustomId(requestModel.getUnique_id());
            try {
                zhipuaiApi.aiModelResultJsonParse(responseVo.getResult().toLowerCase(), gson, zhiPuAiContentModel, zhiPuAiContentModelList);
            } catch (Exception e) {
                extracted(responseVo, requestModel, recordModelList);
                zhiPuAiContentModel.setCustomId(requestModel.getUnique_id());
                zhiPuAiContentModelList.add(zhiPuAiContentModel);
            }
        } else {
            log.error("实时模型处理节点处理失败第二次重试！：{}", JSONUtil.toJsonStr(requestModel));
            responseVo = compoundLlmAiApi.generateAiResponse(requestModel);
            if (responseVo != null && responseVo.getStatus().equals("success")) {
                zhiPuAiContentModel.setCustomId(requestModel.getUnique_id());
                try {
                    zhipuaiApi.aiModelResultJsonParse(responseVo.getResult().toLowerCase(), gson, zhiPuAiContentModel, zhiPuAiContentModelList);
                } catch (Exception e) {
                    extracted(responseVo, requestModel, recordModelList);
                    zhiPuAiContentModel.setCustomId(requestModel.getUnique_id());
                    zhiPuAiContentModelList.add(zhiPuAiContentModel);
                }
            } else {
                //更新失败状态
                List<String> customIds = new ArrayList<String>();
                customIds.add(context.getDataModel().getId());
                modMetaDataAnalysisService.batchUpdateStatus(customIds, 3);
                zhiPuAiContentModel.setCustomId(requestModel.getUnique_id());
                zhiPuAiContentModelList.add(zhiPuAiContentModel);
                log.error("实时模型处理节点第二次重试处理失败！：{}", JSONUtil.toJsonStr(requestModel));

            }
        }
        if (CollectionUtil.isNotEmpty(zhiPuAiContentModelList)) {
            if (CollectionUtil.isNotEmpty(zhiPuAiContentModelList)) {
                context.setZhiPuAiContentModelList(zhiPuAiContentModelList);
            }
        }


    }

    private void extracted(ThirdPartyAIContext context) throws Exception {
        ModMetaDataAnalysisEntity entity=new ModMetaDataAnalysisEntity();
        entity.setNewId(context.getDataModel().getId());
        entity.setWorkId(context.getDataModel().getWorkId());
        entity.setClientId(context.getDataModel().getClientId());
        entity.setContent(context.getDataModel().getContent());
        entity.setCreateTime(LocalDateTime.now());
        entity.setModelType(context.getDataModel().getModelType());
        entity.setContentType(context.getDataModel().getSource());
        modMetaDataAnalysisMapper.insert(entity);
//        analysisProducer.pushDataToModel(MessageDTO.builder().source(context.getDataModel().getSource()).data(context.getDataModel()).build());
    }

    private void extracted(AIResponseVo responseVo, AIRequestModel requestModel, List<ModLlmReturnResultExceptionRecordModel> recordModelList) {
        ModLlmReturnResultExceptionRecordModel recordModel = new ModLlmReturnResultExceptionRecordModel();
        recordModel.setData(responseVo.getResult().toLowerCase());
        recordModel.setNewId(IdWorker.getId());
        recordModel.setBatchId(requestModel.getUnique_id());
        recordModelList.add(recordModel);
        iModLlmReturnResultExceptionRecordService.saveReturnResultExceptionData(recordModelList);
    }
}

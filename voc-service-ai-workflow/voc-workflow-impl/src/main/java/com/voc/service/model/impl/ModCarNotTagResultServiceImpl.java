package com.voc.service.model.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.model.api.IModCarNotTagResultService;
import com.voc.service.model.entity.ModCaNotTagResultEntity;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.ModCarNotTagResultMapper;
import com.voc.service.model.model.ModCaNotTagResultModel;
import com.voc.service.model.producers.kafka.ModelResultProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * @Title: ModCaTagResultServiceImpl
 * @Package: com.voc.service.model.impl
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/6 10:14
 * @Version:1.0
 */
@Service
public class ModCarNotTagResultServiceImpl extends ServiceImpl<ModCarNotTagResultMapper, ModCaNotTagResultEntity>
        implements IModCarNotTagResultService {

    @Autowired
    ModelConvertMapperService modelConvertMapperService;

    @Autowired
    ModelResultProducer modelResultProducer;

    @Override
    public List<ModCaNotTagResultModel> findCarNotTagResult(Set<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return null;
        }
        List<ModCaNotTagResultEntity> modCaNotTagResultEntities = this.list(new QueryWrapper<ModCaNotTagResultEntity>()
                .in("original_id", ids));
        return modelConvertMapperService.cenvertToModCarNotTagResultList(modCaNotTagResultEntities);
    }

    @Override
    public boolean add(String clientId, List<ModCaNotTagResultModel> list) {
        List<ModCaNotTagResultEntity> entity = modelConvertMapperService.cenvertToModCarNotTagResultEntityList(list);
        modelResultProducer.pushCarNotTagData(MessageDTO.builder().source(clientId).data(entity).build());
        return Boolean.TRUE;
    }
}

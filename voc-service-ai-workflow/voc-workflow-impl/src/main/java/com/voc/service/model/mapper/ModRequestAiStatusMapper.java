package com.voc.service.model.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voc.service.model.entity.ModRequestAiStatusEntity;
import com.voc.service.model.model.ModRequestAiStatusModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @创建者: cuick
 * @创建时间: 2024/6/11 13:23
 * @描述:
 **/
@Mapper
@Repository
public interface ModRequestAiStatusMapper extends BaseMapper<ModRequestAiStatusEntity> {

    List<ModRequestAiStatusModel> findStatusData(@Param("ids") List<String> ids);

    List<ModRequestAiStatusModel> findStatusList(@Param("ids") List<String> ids);
}

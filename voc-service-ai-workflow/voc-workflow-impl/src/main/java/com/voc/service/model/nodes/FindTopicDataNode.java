package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.voc.service.model.api.IModelMilvusResultDataService;
import com.voc.service.model.config.AIWorkflowConfig;
import com.voc.service.model.model.VoiceClipRequestDataModel;
import com.voc.service.model.model.VoiceClipResultData;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ModelContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "findTopicDataNode", name = "查询topic匹配值节点")
public class FindTopicDataNode extends AbstractNode {
    private static final Logger log = LoggerFactory.getLogger(FindTopicDataNode.class);
    @Autowired
    private IModelMilvusResultDataService modelMilvusResultDataService;
    @Autowired
    AIWorkflowConfig aiWorkflowConfig;

    @Override
    public void process() throws Exception {
        try {
            ModelContext context = this.getRequestData();
            VoiceClipRequestDataModel processingDataset = context.getInputDataset();
            if ("opinion".equalsIgnoreCase(context.getTag())) {
//                processingDataset.setCollectionName("opinion");
                processingDataset.setCollectionName(aiWorkflowConfig.getOpinionCollectionName());
                processingDataset.setVectorFieldName(aiWorkflowConfig.getOpinionVectorFieldName());
//                processingDataset.setFieldNames(Arrays.asList("id", "topic", "opinion"));
                processingDataset.setFieldNames(aiWorkflowConfig.getOpinionFieldNames());
            } else if ("scenario".equalsIgnoreCase(context.getTag())) {
                processingDataset.setCollectionName(aiWorkflowConfig.getScenarioCollectionName());
                processingDataset.setVectorFieldName(aiWorkflowConfig.getScenarioVectorFieldName());
                processingDataset.setFieldNames(aiWorkflowConfig.getScenarioFieldNames());
            } else {
                throw new Exception("未找到匹配tag值");
            }
            //根据模型数据集查询向量库
            final List<VoiceClipResultData> milvusResultData = modelMilvusResultDataService.findMilvusResultData(processingDataset, context.getEmbeddingList());
            if (CollUtil.isEmpty(milvusResultData)) {
                log.warn("搜索与 opinion 最匹配的topic无结果! {}", processingDataset);
                //无结果时，需要将未命中数据项补齐
                context.setProcessingUnLabeledDataset(Arrays.asList(
                        VoiceClipResultData.builder().id(processingDataset.getNew_id()).build()));
                context.setProcessingLabeledDataset(Collections.EMPTY_LIST);
            } else {
                context.setProcessingUnLabeledDataset(Collections.EMPTY_LIST);
                context.setProcessingLabeledDataset(milvusResultData);
            }

        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        ModelContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getTag()), "getTag cannot be empty");
        Assert.isTrue(ObjectUtils.isNotEmpty(context.getInputDataset()), "inputDataset cannot be empty");
//        Assert.isTrue(CollUtil.isNotEmpty(context.getEmbeddingList()), "getEmbeddingList cannot be empty");

        return true;
    }

}

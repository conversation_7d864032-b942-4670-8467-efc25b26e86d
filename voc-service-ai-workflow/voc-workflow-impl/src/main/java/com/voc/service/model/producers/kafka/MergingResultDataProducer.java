package com.voc.service.model.producers.kafka;

import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.voc.service.analysis.dto.MessageDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CatListener
 * @createTime 2024年03月14日 9:46
 * @Copyright cuick
 * @Description 模型计算normalizer阶段最后步骤【数据合并】
 */
@Component("mergingResultData.producer.kafka")
public class MergingResultDataProducer {
    public static final String TOPIC_EVENT_MODIFY = "VDP_mergingResultDataEvent_modify";
    public static final String TOPIC_EVENT_SAVE = "VDP_mergingResultDataEvent_save";
    @Autowired
    KafkaTemplate<String, String> kafkaTemplate;

    /*public void pushEvent(MessageDTO msg) throws Exception {
        log.info("推送数据 {}", msg.getType());
        Assert.isTrue(StrUtil.isNotBlank(msg.getType()), "getType cannot be empty");
        Assert.isTrue(ObjUtil.isNotEmpty(msg.getData()), "getData cannot be empty");
        final String sendText = JSONUtil.toJsonStr(msg, JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
        kafkaTemplate.send(TOPIC_EVENT_MODIFY, sendText);
    }*/


    public void pushMergingResultData(MessageDTO msg) {
        List<Object> list = (List<Object>) msg.getData();
        for (Object obj : list) {
            String sendText = JSONUtil.toJsonStr(obj, JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
            kafkaTemplate.send(TOPIC_EVENT_SAVE, sendText);
        }
    }
}


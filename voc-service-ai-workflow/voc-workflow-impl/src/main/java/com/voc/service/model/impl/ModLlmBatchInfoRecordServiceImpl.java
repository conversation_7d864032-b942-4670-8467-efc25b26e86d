package com.voc.service.model.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.model.api.IModLlmBatchInfoRecordService;
import com.voc.service.model.entity.ModLlmBatchInfoRecord;
import com.voc.service.model.enums.BatchStatusEnum;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.ModLlmBatchInfoRecordMapper;
import com.voc.service.model.vo.ModLlmBatchInfoRecordVo;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【mod_llm_batch_info_record(大模型批次信息记录表)】的数据库操作Service实现
 * @createDate 2024-08-01 13:45:36
 */
@Service
public class ModLlmBatchInfoRecordServiceImpl extends ServiceImpl<ModLlmBatchInfoRecordMapper, ModLlmBatchInfoRecord>
        implements IModLlmBatchInfoRecordService {

    @Autowired
    ModelConvertMapperService modelConvertMapperService;

    @Override
    public List<ModLlmBatchInfoRecordVo> findBatchIdList(List<String> statusList) {
        if (CollectionUtil.isEmpty(statusList)) {
            return null;
        }
        List<ModLlmBatchInfoRecord> entityList = this.list(
                new QueryWrapper<ModLlmBatchInfoRecord>()
                        .notIn("batch_status", statusList)
        );
        if (CollectionUtil.isEmpty(entityList)) {
            return null;
        }
        return modelConvertMapperService.convertToModLlmBatchInfoRecordEntityListVo(entityList);
    }


    @Override
    public String findOutFileId(List<String> statusList, String batchId) {
        if (CollectionUtil.isEmpty(statusList)) {
            return null;
        }
        ModLlmBatchInfoRecord llmBatchInfoRecord = this.getOne(
                new QueryWrapper<ModLlmBatchInfoRecord>()
                        .in("batch_status", statusList)
                        .eq("batch_id", batchId)

        );
        if (ObjectUtils.isEmpty(llmBatchInfoRecord) || StringUtils.isEmpty(llmBatchInfoRecord.getOutputFileId())) {
            return null;
        }
        return llmBatchInfoRecord.getOutputFileId();
    }


    @Override
    public Long findRecordByBatchId(String batchId) {
        if (StringUtils.isEmpty(batchId)) {
            return 0L;
        }
        List<String> statusList = Arrays.asList(
                BatchStatusEnum.COMPLETED.getCode(),
                BatchStatusEnum.EXPIRED.getCode(),
                BatchStatusEnum.CANCELLING.getCode(),
                BatchStatusEnum.CANCELLED.getCode());
        return this.count(new QueryWrapper<ModLlmBatchInfoRecord>()
                .eq("batch_id", batchId)
                .notIn("batch_status", statusList)
        );
    }

    @Override
    public int modifyAIModelBatchStatus(String batchId, String status, String inputFileId, String errorFileId) {
        if (StringUtils.isEmpty(batchId)) {
            return 0;
        }
        UpdateWrapper<ModLlmBatchInfoRecord> wrapper = new UpdateWrapper<>();
        wrapper.eq("batch_id", batchId);
        wrapper.set("batch_status", status);
        wrapper.set("output_file_id", inputFileId);
        wrapper.set("error_file_id", errorFileId);
        wrapper.set("update_time", LocalDateTime.now());
        return this.baseMapper.update(null, wrapper);
    }

    @Override
    public int modifyToDone(String batchId) {
        if (StringUtils.isEmpty(batchId)) {
            return 0;
        }
        UpdateWrapper<ModLlmBatchInfoRecord> wrapper = new UpdateWrapper<>();
        wrapper.eq("batch_id", batchId);
        wrapper.set("done", 1);
        wrapper.set("update_time", LocalDateTime.now());
        return this.baseMapper.update(null, wrapper);
    }

    @Override
    public ModLlmBatchInfoRecordVo getByBatchId(String batchId) {
        QueryWrapper<ModLlmBatchInfoRecord> wrapper = new QueryWrapper();
        wrapper.eq("batch_id", batchId);
        ModLlmBatchInfoRecord record = this.getOne(wrapper);
        return modelConvertMapperService.convertToModLlmBatchInfoRecordVo(record);
    }
}





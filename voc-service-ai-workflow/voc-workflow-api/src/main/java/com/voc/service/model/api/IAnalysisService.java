package com.voc.service.model.api;

import java.util.Set;

/**
 * @Title: IAnalysisService
 * @Package: com.voc.service.model.api
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 17:20
 * @Version:1.0
 */
public interface IAnalysisService {
    void markDataStatus(Set<String> ids, long period);

    void removeMarkDataStatus(Set<String> ids);

    Set<String> effectiveDataset(Set<String> ids);


    void removeDataStatus(Set<String> ids);

//    Map<String, Integer> getDataStatusList(Set<String> ids );

//    Set<String> addDataStatusList(Set<String> ids);

//    Set<String> updateDataStatus(int status, Set<String> ids,int conditionStatus );

//    Set<String> readAllUnprocessedIds();

//    boolean isExistUnprocessedDataset(String id);


}

package com.voc.service.model.nodes;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.voc.service.common.util.IdWorker;
import com.voc.service.model.api.IModCaTagResultService;
import com.voc.service.model.api.IModCarSceneResultService;
import com.voc.service.model.model.ModCaTagResultModel;
import com.voc.service.model.model.ModCarSceneResultModel;
import com.voc.service.model.model.VoiceClipRequestDataModel;
import com.voc.service.model.model.VoiceClipResultData;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ModelContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryException;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.*;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "saveTagMappingResultDataNode", name = "推送数据到结果明细接收队列节点")
public class SaveTagMappingResultDataNode extends AbstractNode {
    private static final Logger log = LoggerFactory.getLogger(SaveTagMappingResultDataNode.class);
    @Autowired
    IModCaTagResultService modCaTagResultService;
    @Autowired
    IModCarSceneResultService modCarSceneResultService;

    @Override
    public void process() throws Exception {
        try {
            ModelContext context = this.getRequestData();
            VoiceClipRequestDataModel inputDataset = context.getInputDataset();
            String clientId = context.getClientId();
            //AI计算后的声音ID
            final String voiceId = inputDataset.getNew_id();
            if ("opinion".equalsIgnoreCase(context.getTag())) {
                List<VoiceClipResultData> processingLabeledDataset = context.getProcessingLabeledDataset();
                List<ModCaTagResultModel> modelList = new ArrayList<>();
                for (VoiceClipResultData voiceClipResultData : processingLabeledDataset) {
                    ModCaTagResultModel modCaTagResultModel = new ModCaTagResultModel();
                    Map<String, Object> extAttrMaps = new HashMap<>();
                    extAttrMaps.put("evalAttrs", StrUtil.isNotBlank(inputDataset.getAspect()) ? inputDataset.getAspect() : "");
                    extAttrMaps.put("evalSubject", StrUtil.isNotBlank(inputDataset.getSubject()) ? inputDataset.getSubject() : "");
                    extAttrMaps.put("evalDesc", StrUtil.isNotBlank(inputDataset.getDesc()) ? inputDataset.getDesc() : "");
                    extAttrMaps.put("normalizedOpinion", StrUtil.isNotBlank(voiceClipResultData.getSim_opinion()) ? voiceClipResultData.getSim_opinion() : "");
                    log.info("获取分数数据值:{}", voiceClipResultData.getScore());
                    extAttrMaps.put("similarity", ObjectUtils.isNotEmpty(voiceClipResultData.getScore()) ? voiceClipResultData.getScore() : "");
                    modCaTagResultModel.setNewId(IdWorker.getId());
                    modCaTagResultModel.setVoiceId(voiceId);
                    modCaTagResultModel.setOriginalId(context.getContentId());
                    modCaTagResultModel.setWorkId(context.getWorkId());
                    modCaTagResultModel.setModelType(context.getModelType());
                    modCaTagResultModel.setContentType(context.getContentType());
                    modCaTagResultModel.setChannelId(context.getChannelId());
                    modCaTagResultModel.setExtFields(extAttrMaps);
                    modCaTagResultModel.setCreateTime(LocalDateTime.now());
                    if (StringUtils.isNotBlank(voiceClipResultData.getBusinessTagName())) {
                        modCaTagResultModel.setBTag(voiceClipResultData.getBusinessTagName());
                        String[] split = voiceClipResultData.getBusinessTagName().split("#");
                        modCaTagResultModel.setBusinessLabelTypeLevelFirst(split[0]);
                        modCaTagResultModel.setBusinessLabelTypeLevelSecond(split[1]);
                        modCaTagResultModel.setBusinessLabelTypeLevelThree(split[2]);
                        modCaTagResultModel.setBusinessLabelTypeLevelFour(split[3]);
                    }
                    if (StringUtils.isNotBlank(voiceClipResultData.getQualityTagName())) {
                        modCaTagResultModel.setQTag(voiceClipResultData.getQualityTagName());
                        String[] split = voiceClipResultData.getQualityTagName().split("#");
                        modCaTagResultModel.setQualityLabelTypeLevelFirst(split[0]);
                        modCaTagResultModel.setQualityLabelTypeLevelSecond(split[1]);
                        modCaTagResultModel.setQualityLabelTypeLevelThree(split[2]);
                        modCaTagResultModel.setQualityLabelTypeLevelFour(split[3]);
                    }
                    modCaTagResultModel.setSentiment(inputDataset.getSentiment());
                    modCaTagResultModel.setIntentionType(inputDataset.getIntent());
                    modCaTagResultModel.setTopic(voiceClipResultData.getTopic());
                    modCaTagResultModel.setOpinion(voiceClipResultData.getSim_opinion());
                    modCaTagResultModel.setFaultLevel(voiceClipResultData.getTagsLevel4());
                    if (StringUtils.isNotBlank(inputDataset.getAspect())) {
                        String keywords = JSON.toJSONString(Collections.singleton(inputDataset.getAspect()));
                        modCaTagResultModel.setKeywords(keywords);
                    }
                    modelList.add(modCaTagResultModel);
                }
                modCaTagResultService.add(clientId, modelList);
            } else if ("scenario".equalsIgnoreCase(context.getTag())) {
                List<VoiceClipResultData> processingLabeledDataset = context.getProcessingLabeledDataset();
                List<ModCarSceneResultModel> list = new ArrayList<>();
                for (VoiceClipResultData voiceClipResultData : processingLabeledDataset) {
                    ModCarSceneResultModel build = ModCarSceneResultModel.builder()
                            .newId(IdWorker.getId())
                            .voiceId(voiceId)
                            .originalId(context.getContentId())
                            .workId(context.getWorkId())
                            .simScenario(voiceClipResultData.getTagsLevel4())
                            .scenarioSchema(voiceClipResultData.getScenario_schema())
                            .score(String.valueOf(voiceClipResultData.getScore()))
                            .channelId(context.getChannelId())
                            .contentType(context.getContentType())
                            .modelType(context.getModelType())
                            .createTime(LocalDateTime.now())
                            .build();
                    list.add(build);
                }
                modCarSceneResultService.add(clientId, list);
            } else {
                throw new Exception("未找到匹配tag值");
            }
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        ModelContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getTag()), "getTag cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(context.getContentId()), "getContentId cannot be empty");
        Assert.isTrue(ObjectUtil.isNotEmpty(context.getProcessingLabeledDataset()), "getProcessingDataset cannot be empty");
//        Assert.isTrue(CollUtil.isNotEmpty(context.getIds()), "getIds cannot be empty");


        return true;
    }

}

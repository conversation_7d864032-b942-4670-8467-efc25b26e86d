package com.voc.service.model.nodes;

import cn.hutool.core.util.StrUtil;
import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ModelContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryException;
import org.springframework.util.Assert;

import java.util.Set;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "recordParseSchemaDataDataSizeNode", name = "记录流程中数据集合数量节点")
public class RecordMatchingSchemaDataSizeNode extends AbstractNode {
    private static final Logger log = LoggerFactory.getLogger(RecordMatchingSchemaDataSizeNode.class);
    @Autowired
    IMergeStatusRecordService mergeStatusRecordService;

    @Override
    public void process() throws Exception {
        try {
            final int processingUnLabeledSize;
            ModelContext context = this.getRequestData();
            if ("one".equalsIgnoreCase(this.getTag())) {
                processingUnLabeledSize = 1;
            } else {
                processingUnLabeledSize = context.getProcessingUnLabeledDataset().size() + context.getProcessingLabeledDataset().size();
            }
            log.info("{}_num , size {}", context.getTag(), processingUnLabeledSize);
            if (processingUnLabeledSize < 1) {
                log.error("获取的流程完成数据集大小异常 {}", context);
                return;
            }
            final String contentId = context.getContentId();
            final String voiceId = context.getInputDataset().getNew_id();

            if ("opinion".equalsIgnoreCase(context.getTag())) {
                //更新记录
                mergeStatusRecordService.updateLabeledDataSize(Set.of(voiceId), processingUnLabeledSize);
            } else if ("scenario".equalsIgnoreCase(context.getTag())) {
                mergeStatusRecordService.updateScenariosDataSize(Set.of(voiceId), processingUnLabeledSize);
            } else if ("brand".equalsIgnoreCase(context.getTag())) {
                mergeStatusRecordService.updateBrandDataSize(Set.of(voiceId), processingUnLabeledSize);
            } else {
                ;
            }
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        ModelContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getTag()), "getTag cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(context.getContentId()), "getContentId cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(context.getInputDataset().getNew_id()), "voiceId cannot be empty");

        return true;
    }

}

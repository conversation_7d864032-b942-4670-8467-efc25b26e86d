package com.voc.service.model.producers.kafka;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.voc.service.analysis.dto.MessageDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CatListener
 * @createTime 2024年03月14日 9:46
 * @Copyright cuick
 * @Description 再推送数据清洗服务前的【数据结果表】
 */
@Component("modelResult.producer.kafka")
public class ModelResultProducer {
    public static final String TOPIC_BRAND_CAR = "VDP_modFtmBrandCarSeries";
    public static final String TOPIC_CAR_NOT_TAG = "VDP_modFtmCarNotTagResult";
    public static final String TOPIC_CAR_SCENE_RESULT = "VDP_modFtmCarSceneResult";
    public static final String TOPIC_CAR_TAG_RESULT = "VDP_modFtmCarTagResult";
    public static final String TOPIC_NOT_CAR_SCENE_RESULT = "VDP_modFtmNotCarSceneResult";
    public static final String TOPIC_LLM_RETURN_RESULT = "VDP_modLlmReturnResultInfoRecord";
    public static final String TOPIC_LLM_EXCEPTION_RESULT = "VDP_modLlmReturnResultExceptionRecord";
    public static final String TOPIC_LLM_RETURN_RESULT_MODIFY = "VDP_modLlmReturnResultInfoRecord_modify";
    public static final String TOPIC_LLM_RETURN_RESULT_MODIFY_EVENT = "VDP_modLlmReturnResultInfoRecord_modify_event";
    @Autowired
    KafkaTemplate<String, String> kafkaTemplate;

    public void pushBrandCarData(MessageDTO msg) {
        Assert.isTrue(StrUtil.isNotBlank(msg.getSource()), "source cannot be empty");
        List<Object> list = (List<Object>) msg.getData();

        for (Object obj : list) {
            Assert.isTrue(StrUtil.isNotBlank(msg.getSource()), "msg.getSource() cannot be empty");
            final String clientId = msg.getSource();
            String sendText = JSONUtil.toJsonStr(obj, JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
            kafkaTemplate.send(TOPIC_BRAND_CAR.concat("_").concat("0"), sendText);
        }
    }


    public void pushCarNotTagData(MessageDTO msg) {
        Assert.isTrue(StrUtil.isNotBlank(msg.getSource()), "source cannot be empty");
        List<Object> list = (List<Object>) msg.getData();

        for (Object obj : list) {
            Assert.isTrue(StrUtil.isNotBlank(msg.getSource()), "msg.getSource() cannot be empty");
            final String clientId = msg.getSource();

            String sendText = JSONUtil.toJsonStr(obj, JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
            kafkaTemplate.send(TOPIC_CAR_NOT_TAG.concat("_").concat("0"), sendText);
        }
    }


    public void pushCarSceneResultData(MessageDTO msg) {
        Assert.isTrue(StrUtil.isNotBlank(msg.getSource()), "source cannot be empty");
        List<Object> list = (List<Object>) msg.getData();

        for (Object obj : list) {
            Assert.isTrue(StrUtil.isNotBlank(msg.getSource()), "msg.getSource() cannot be empty");
            final String clientId = msg.getSource();

            String sendText = JSONUtil.toJsonStr(obj, JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
            kafkaTemplate.send(TOPIC_CAR_SCENE_RESULT.concat("_").concat("0"), sendText);
        }
    }


    public void pushCarTagResultData(MessageDTO msg) {
        Assert.isTrue(StrUtil.isNotBlank(msg.getSource()), "source cannot be empty");
        List<Object> list = (List<Object>) msg.getData();

        for (Object obj : list) {
            Assert.isTrue(StrUtil.isNotBlank(msg.getSource()), "msg.getSource() cannot be empty");
            final String clientId = msg.getSource();

            String sendText = JSONUtil.toJsonStr(obj, JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
            kafkaTemplate.send(TOPIC_CAR_TAG_RESULT.concat("_").concat("0"), sendText);
        }
    }


    public void pushNotCarSceneResultData(MessageDTO msg) {
        Assert.isTrue(StrUtil.isNotBlank(msg.getSource()), "source cannot be empty");
        List<Object> list = (List<Object>) msg.getData();

        for (Object obj : list) {
            Assert.isTrue(StrUtil.isNotBlank(msg.getSource()), "msg.getSource() cannot be empty");
            final String clientId = msg.getSource();

            String sendText = JSONUtil.toJsonStr(obj, JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
            kafkaTemplate.send(TOPIC_NOT_CAR_SCENE_RESULT.concat("_").concat("0"), sendText);
        }
    }


    public void pushLlmReturnResultData(MessageDTO msg) {
        Assert.isTrue(StrUtil.isNotBlank(msg.getSource()), "source cannot be empty");
        List<Object> list = (List<Object>) msg.getData();

        for (Object obj : list) {
            Assert.isTrue(StrUtil.isNotBlank(msg.getSource()), "msg.getSource() cannot be empty");
            final String clientId = msg.getSource();

            String sendText = JSONUtil.toJsonStr(obj, JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
            kafkaTemplate.send(TOPIC_LLM_RETURN_RESULT.concat("_").concat("0"), sendText);
        }
    }


    public void pushLlmReturnExceptionData(MessageDTO msg) {
        List<Object> list = (List<Object>) msg.getData();
        for (Object obj : list) {
            String sendText = JSONUtil.toJsonStr(obj, JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
            kafkaTemplate.send(TOPIC_LLM_EXCEPTION_RESULT.concat("_").concat("0"), sendText);
        }
    }

    public void pushModifyData(MessageDTO msg) {

        String sendText = JSONUtil.toJsonStr(msg, JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
        kafkaTemplate.send(TOPIC_LLM_RETURN_RESULT_MODIFY, sendText);
    }

    public void pushModifyEvent(MessageDTO msg) {
        String sendText = JSONUtil.toJsonStr(msg, JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
        kafkaTemplate.send(TOPIC_LLM_RETURN_RESULT_MODIFY_EVENT, sendText);
    }
}


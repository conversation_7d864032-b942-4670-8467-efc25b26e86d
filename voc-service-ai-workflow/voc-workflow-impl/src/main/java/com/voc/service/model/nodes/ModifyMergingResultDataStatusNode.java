package com.voc.service.model.nodes;

import com.voc.service.model.api.IMergeStatusRecordService;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.AnalysisDataContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Set;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "modifyMergingResultDataStatusNode", name = "修改合并后数据推送完成状态节点")
public class ModifyMergingResultDataStatusNode extends AbstractNode {
    @Autowired
    IMergeStatusRecordService mergeStatusRecordService;

    @Override
    public void process() throws Exception {
        try {
            AnalysisDataContext context = this.getRequestData();
            final Set<String> ids = context.getIds();
            mergeStatusRecordService.modifyToDoneDB(ids);

        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        return true;
    }

}

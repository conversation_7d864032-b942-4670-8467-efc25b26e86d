package com.voc.service.model.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.common.util.ServiceContextHolder;
import com.voc.service.model.api.IScenariosTagsLevelMappingService;
import com.voc.service.model.entity.ScenariosTagsLevelMappingEntity;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.ScenariosTagsLevelMappingMapper;
import com.voc.service.model.model.ScenariosTagsLevelMappingModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @创建者: fanrong
 * @创建时间: 2024/8/6 下午4:49
 * @描述:
 **/
@Service
public class ScenariosTagsLevelMappingServiceImpl extends ServiceImpl<ScenariosTagsLevelMappingMapper, ScenariosTagsLevelMappingEntity> implements IScenariosTagsLevelMappingService {

    @Autowired
    ModelConvertMapperService convertMapperService;

    @CreateCache(area = "VDP", name = ":",  cacheType = CacheType.REMOTE)
    private Cache<String, Map<String, List<ScenariosTagsLevelMappingModel>>> cache;

    @Override
    public Map<String, List<ScenariosTagsLevelMappingModel>> findAllScenariosTagsLevelMapping() {

        return cache.computeIfAbsent(this.getScenariosTagsLevelMappingKey("scenariosTagsLevelMapping"), (key) -> {
            List<ScenariosTagsLevelMappingEntity> scenariosTagsLevelEntities = this.baseMapper.selectList(new QueryWrapper<>());
            List<ScenariosTagsLevelMappingModel> scenariosTagsLevelMappingModels = convertMapperService.cenvertToScenariosTagsLevelMappingModelList(scenariosTagsLevelEntities);
            Map<String, List<ScenariosTagsLevelMappingModel>> collect = scenariosTagsLevelMappingModels.stream().collect(Collectors.groupingBy(ScenariosTagsLevelMappingModel::getLevel4));
            Map<String, List<ScenariosTagsLevelMappingModel>> md5KeyMap = new HashMap<>();
            collect.entrySet().stream().forEach(e -> {
                md5KeyMap.put(DigestUtil.md5Hex(e.getKey()), e.getValue());
            });
            return md5KeyMap;
        });
    }

    private String getScenariosTagsLevelMappingKey(Object... params){
        return StrUtil.format("{}:scenariosTagsLevelMapping:{}", ServiceContextHolder.getSystemId(),params);
    }
}

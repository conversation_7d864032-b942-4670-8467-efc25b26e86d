package com.voc.service.model.nodes.abstracts;

import com.voc.service.common.util.StopWatch;
import com.yomahub.liteflow.slot.DefaultContext;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Title: AbstractContext
 * @Package: com.voc.service.model.nodes.context
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 17:15
 * @Version:1.0
 */

@Data
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AbstractContext extends DefaultContext implements Serializable {

    StopWatch stopWatch;

    public AbstractContext() {
        stopWatch = new StopWatch();
    }

    String clientId;
    String workId;
    String dataId;
}

package com.voc.service.model.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.common.util.ServiceContextHolder;
import com.voc.service.components.mybatis.annotation.SwitchClientDS;
import com.voc.service.model.api.ICarAndDimChannelMappingService;
import com.voc.service.model.entity.CarAndDimChannelMappingEntity;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.CarAndDimChannelMappingMapper;
import com.voc.service.model.model.CarAndDimChannelMappingModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ConcurrentReferenceHashMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Title: CarAndDimChannelMappingServiceImpl
 * @Package: com.voc.service.model.impl
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/1 16:40
 * @Version:1.0
 */
@Service
public class CarAndDimChannelMappingServiceImpl extends ServiceImpl<CarAndDimChannelMappingMapper, CarAndDimChannelMappingEntity>
        implements ICarAndDimChannelMappingService {
    @Autowired
    ModelConvertMapperService convertMapperService;
    @CreateCache(area = "VDP", name = ":",  cacheType = CacheType.REMOTE)
    private Cache<String, Map<String, List<CarAndDimChannelMappingModel>>> clientsTopicMappingCache;

    private static final String CLIENTS_TOPIC_MAPPING_KEY = "{}:clients_topic_mapping:{}";

    private String getClientsTopicMappingCacheKey(Object... params){
        return StrUtil.format(CLIENTS_TOPIC_MAPPING_KEY, ServiceContextHolder.getSystemId(),params);
    }


    @SwitchClientDS
    @Override
    public Map<String, List<CarAndDimChannelMappingModel>> findAlMd5Maps(String clientId) {
        Assert.isTrue(StrUtil.isNotBlank(clientId), "clientId cannot be empty");
        Map<String, List<CarAndDimChannelMappingModel>> map = clientsTopicMappingCache.get(this.getClientsTopicMappingCacheKey(clientId));
        if (CollUtil.isEmpty(map)) {
            List<CarAndDimChannelMappingEntity> list = this.baseMapper.selectcarAndDimMappings();

            List<CarAndDimChannelMappingModel> modelList = convertMapperService.cenvertToCarAndDimChannelMappingModelList(list);
            Map<String, List<CarAndDimChannelMappingModel>> maps = new ConcurrentReferenceHashMap<>();

            modelList.stream()
                    .map(model -> {
                        model.setMd5(this.getMd5(model.getCar(), model.getDim()));
                        return model;
                    })
                    .forEach(model -> {
                        if (maps.containsKey(model.getMd5())) {
                            maps.get(model.getMd5()).add(model);
                        } else {
                            maps.put(model.getMd5(), new ArrayList<>(Arrays.asList(model)));
                        }
                    });
            map = maps;
            clientsTopicMappingCache.put(this.getClientsTopicMappingCacheKey(clientId), map);
        }

        return map;
    }

    @Override
    public String getMd5(String car, String dim) {
        String car_ = StrUtil.isBlank(car) ? "" : StrUtil.trim(car);
        String dim_ = StrUtil.isBlank(dim) ? "" : StrUtil.trim(dim);
        return DigestUtil.md5Hex(car_.concat(dim_));
    }

    @SwitchClientDS
    @Override
    public List<CarAndDimChannelMappingModel> getModelList(String clientId, String car, String dim) {
        Map<String, List<CarAndDimChannelMappingModel>> maps = this.findAlMd5Maps(clientId);
        final String md5Str = this.getMd5(car, dim);

        return maps.get(md5Str);
    }
}

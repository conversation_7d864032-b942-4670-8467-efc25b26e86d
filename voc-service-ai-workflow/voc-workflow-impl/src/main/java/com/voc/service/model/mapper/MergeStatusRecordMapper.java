package com.voc.service.model.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voc.service.model.entity.MergeStatusRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @创建者: cuick
 * @创建时间: 2024/6/11 13:23
 * @描述:
 **/
@Mapper
@Repository
public interface MergeStatusRecordMapper extends BaseMapper<MergeStatusRecordEntity> {

    List<MergeStatusRecordEntity> findUnpushedData(int pushAnalysisServiceMqDatasetSize);

    List<MergeStatusRecordEntity> findCompletedData(@Param("limit") int limit, @Param("current_time") LocalDateTime current_time);
}

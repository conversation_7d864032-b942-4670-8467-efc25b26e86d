package com.voc.service.model.consumers.kafka;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.analysis.dto.MessageExt;
import com.voc.service.common.util.IdWorker;
import com.voc.service.model.api.IAnalysisService;
import com.voc.service.model.api.IMessageQueueService;
import com.voc.service.model.config.AIWorkflowConfig;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.voc.service.model.producers.kafka.ReturnResultInfoRecordProducer;
import com.voc.service.model.producers.kafka.UnprocessedResultDataProducer;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import jakarta.annotation.Resource;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CatListener
 * @createTime 2024年03月14日 9:46
 * @Copyright cuick
 */
@Component("unprocessedResultData.cusumer.kafka")
public class UnprocessedResultDataCusumer {
    private static final Logger log = LoggerFactory.getLogger(UnprocessedResultDataCusumer.class);
    @Autowired
    KafkaTemplate<String, String> kafkaTemplate;
    @Resource
    private RedissonClient redissonClient;
    RLock rlock;
    @Resource
    private FlowExecutor flowExecutor;
    @Autowired
    IAnalysisService analysisService;
    @Autowired
    ReturnResultInfoRecordProducer returnResultInfoRecordProducer;
    @Autowired
    IMessageQueueService messageQueueService;
    @Autowired
    AIWorkflowConfig config;
    @Autowired
    RedisTemplate redisTemplate;

    final String chainId = "invoking_raw_normalizer_flow_v2";

    static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss", Locale.CHINA);

    static final String last_cusumer_time = "ai-workflow".concat("::last_cusumer::");

    /*
    public void init() {
        rlock = redissonClient.getLock(chainId);
    }*/

    @KafkaListener(topics = {UnprocessedResultDataProducer.TOPIC_DATA}, groupId = "${model.ai_workflow.llm.customer.group.to_model_topic_group}")
    public void onMessage(String message) {
        log.debug(">>>>>>> 收到 {} {}的请求 <<<<<<<<<<<<<<", UnprocessedResultDataProducer.TOPIC_DATA, message);
        MessageDTO dto = null;
        try {
            dto = JSONUtil.toBean(message, MessageDTO.class);
            if (ObjUtil.isNull(dto)) {
                //           if (true) {
                log.error("dto {}", dto);
                return;
            }
            final List<String> idsList = JSONUtil.toList(String.valueOf(dto.getData()), String.class);
            log.info("接收到的IDS {}", idsList);
            if (CollUtil.isEmpty(idsList)) {
                log.error("ID集合未空 {}", message);
                return;
            }

            final MessageExt originalIdExt = dto.getExt().stream().filter(ext -> ext.getKey().equals("originalId")).findFirst().get();
            final String originalId = String.valueOf(originalIdExt.getValue());
            Assert.isTrue(StrUtil.isNotBlank(originalId), "originalId cannot be empty");

            AIResultDataContext context = AIResultDataContext.builder().build();
            MessageExt msgExt = null;
            try {
                rlock = redissonClient.getLock(originalId);
                if (!rlock.isLocked()) {
                    rlock.lock();
                    context.setIds(new HashSet<>(idsList));
                    context.setWorkId(IdWorker.getId());
                    context.setOriginalId(originalId);
                    context.getStopWatch().start("执行 ".concat(chainId));

                    LiteflowResponse response = flowExecutor.execute2Resp(chainId, context,context.getWorkId());
                    if (!response.isSuccess()) {
                        log.error("workId:{}  {}", context.getWorkId(), response.getCause());
                        throw response.getCause();
                    }
                    msgExt = dto.getExt().stream().filter(ext -> ext.getKey().equals("message")).findFirst().get();
                    if (ObjUtil.isNull(msgExt) || ObjUtil.isNull(msgExt.getValue())) {
                        log.error("无法重新放入 mod_llm_return_result_info_record 数据 {}", dto);
                    }
                } else {
                    log.error("内容已处理完成 originalId：{}", originalId);
                }
            } catch (Exception e) {
                log.error("error:{} ids: {}", e.getMessage(), idsList);
                log.error(e.getMessage(), e);
                redisTemplate.delete(ReturnResultInfoRecordRealTimeConsumer.CHECK_ORIGINALID.concat(originalId));
                returnResultInfoRecordProducer.pushData(String.valueOf(msgExt.getValue()));
            } finally {
                redisTemplate.delete(ReturnResultInfoRecordRealTimeConsumer.CHECK_ORIGINALID.concat(originalId));
                returnResultInfoRecordProducer.pushData(String.valueOf(msgExt.getValue()));
                if (rlock.isHeldByCurrentThread()) {
                    rlock.unlock();
                }
                log.info("workId {} 完成 ", context.getWorkId());
            }

        } catch (Exception e) {
            log.error("unprocessedResultData_push异常:{}", e.getMessage());
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 再接收到优先级高的topic数据时，暂停权重低的topic消费， 之后通过参数[resume_consumption_interval]判断最后一次消费数据的时间间隔
     * 超出此时间范围时，将自动恢复权重低的topic消费
     *
     * @param message
     */
    @KafkaListener(topics = {UnprocessedResultDataProducer.TOPIC_DATA_HIGHT_LEVEL}, groupId = "${model.ai_workflow.llm.customer.group.to_model_topic_group}")
    public void onMessageHightLevel(String message) {
        log.info("开启优先执行逻辑");
        this.pauseAction();
        log.info("开始处理数据");
        this.onMessage(message);
    }


    /**
     * 暂停消费
     */
    @Scheduled(cron = "0/5 * * * * ?")
    public void pauseConsumerListener() {
        Object data = redisTemplate.opsForValue().get(last_cusumer_time.concat(UnprocessedResultDataProducer.TOPIC_DATA_HIGHT_LEVEL));
        Optional.ofNullable(data).ifPresent(o -> {
            if (LocalDateTime.parse(String.valueOf(data), formatter).plusSeconds(config.getResumeConsumptionInterval())
                    .isAfter(LocalDateTime.now())) {
                //暂停
                this.pauseAction();
            }
        });
    }

    /**
     * 恢复消费
     */
    @Scheduled(cron = "0/5 * * * * ?")
    public void resumeConsumerListener() {
        Object data = redisTemplate.opsForValue().get(last_cusumer_time.concat(UnprocessedResultDataProducer.TOPIC_DATA_HIGHT_LEVEL));
        Optional.ofNullable(data).ifPresent(o -> {
            if (LocalDateTime.parse(String.valueOf(data), formatter).plusSeconds(config.getResumeConsumptionInterval())
                    .isBefore(LocalDateTime.now())) {
                //暂停
                this.resumeAction();
            }
        });
    }

    /**
     * 暂停
     */
    private void pauseAction() {
        //暂停topic消费
        Set.of(UnprocessedResultDataProducer.TOPIC_DATA).stream()
//                .filter(topic -> messageQueueService.isListenerRunning(topic))
                .forEach(topic -> {
                    messageQueueService.stopListener(Set.of(topic));

                    log.info("暂停topic消费 {}", UnprocessedResultDataProducer.TOPIC_DATA);
                    //记录最后一次消费时间
                    redisTemplate.opsForValue().set(last_cusumer_time.concat(UnprocessedResultDataProducer.TOPIC_DATA_HIGHT_LEVEL)
                            , LocalDateTime.now().format(formatter));
                    log.info("记录最后一次消费时间 ");
                });
    }

    private void resumeAction() {
        //暂停topic消费
        Set.of(UnprocessedResultDataProducer.TOPIC_DATA).stream()
//                .filter(topic -> !messageQueueService.isListenerRunning(topic))
                .forEach(topic -> {
                    messageQueueService.startListener(Set.of(topic));
                    //记录标记
                });
    }
}


package com.voc.service.model.nodes;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.voc.service.model.api.IAnalysisService;
import com.voc.service.model.api.IModFtmBrandCarSeriesService;
import com.voc.service.model.api.IModRequestAiStatusService;
import com.voc.service.model.model.ModFtmBrandCarSeriesModel;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.AIResultDataContext;
import com.voc.service.model.vo.BrandCarVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "saveBrandTagMappingResultDataNode", name = "推送brand数据到结果明细接收队列节点")
public class SaveBrandTagMappingResultDataNode extends AbstractNode {

    private static final Logger log = LoggerFactory.getLogger(SaveBrandTagMappingResultDataNode.class);
    @Autowired
    IModFtmBrandCarSeriesService iModFtmBrandCarSeriesService;

    @Autowired
    IAnalysisService iAnalysisService;

    @Autowired
    IModRequestAiStatusService iModRequestAiStatusService;

    @Override
    public void process() throws Exception {
        try {
            AIResultDataContext context = this.getRequestData();
            List<BrandCarVo> brandCarVoList = context.getBrandCarVoList();
            String clientId = context.getClientId();
            if (CollectionUtil.isEmpty(brandCarVoList)) {
                return;
            }
            List<ModFtmBrandCarSeriesModel> modFtmBrandCarSeriesModels = new ArrayList<>();
            Map<Integer, List<ModFtmBrandCarSeriesModel>> maps = new HashMap<>();
            for (BrandCarVo brandCarVo : brandCarVoList) {
                String originalId = brandCarVo.getId();
                List<BrandCarVo.BrandCar> brandCarList = brandCarVo.getResult();
                if (CollectionUtil.isEmpty(brandCarList)) {
                    continue;
                }
                for (BrandCarVo.BrandCar brandCar : brandCarList) {
                    if (StringUtils.isEmpty(brandCar.getVoiceId())) {
                        continue;
                    }
//                    newIdList.add(brandCar.getVoiceId());
                    ModFtmBrandCarSeriesModel modFtmBrandCarSeriesModel = new ModFtmBrandCarSeriesModel();
//                    modFtmBrandCarSeriesModel.setNewId(IdWorker.getId());
                    modFtmBrandCarSeriesModel.setNewId(brandCar.getVoiceId());
                    modFtmBrandCarSeriesModel.setVoiceId(brandCar.getVoiceId());
                    modFtmBrandCarSeriesModel.setWorkId(context.getWorkId());
                    modFtmBrandCarSeriesModel.setModelType(context.getModelType());
                    modFtmBrandCarSeriesModel.setContentType(context.getContentType());
                    modFtmBrandCarSeriesModel.setChannelId(context.getChannelId());
                    modFtmBrandCarSeriesModel.setOriginalId(originalId);
                    modFtmBrandCarSeriesModel.setBrandCode(brandCar.getBrandCode());
                    modFtmBrandCarSeriesModel.setBrand(brandCar.getBrand());
                    modFtmBrandCarSeriesModel.setCarSeries(brandCar.getCarSeries());
                    modFtmBrandCarSeriesModel.setCarSeriesCode(brandCar.getCarSeriesCode());
                    modFtmBrandCarSeriesModel.setCreateTime(LocalDateTime.now());
                    modFtmBrandCarSeriesModels.add(modFtmBrandCarSeriesModel);
                }
            }
            context.getStopWatch().stop();
            context.getStopWatch().start("推送brand数据到结果明细接收队列节点-》".concat("saveModFtmBrandCarSeries"));
            int count = iModFtmBrandCarSeriesService.saveModFtmBrandCarSeries(clientId, modFtmBrandCarSeriesModels);
            context.getStopWatch().stop();
            context.getStopWatch().start("推送brand数据到结果明细接收队列节点-》".concat("整车标签服务调用记录添加db"));

            if (CollUtil.isNotEmpty(modFtmBrandCarSeriesModels)) {
                maps.put(2, modFtmBrandCarSeriesModels);
                this.sendPrivateDeliveryData("recordMatchingSchemaDataSizeV2Node", maps);
            }

            context.getStopWatch().stop();
            log.info("模型品牌车系保存成功:{}", count);
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        AIResultDataContext context = this.getRequestData();
        return true;
    }

}

package com.voc.service.model.impl;

import ai.onnxruntime.OrtSession;
import cn.hutool.core.lang.Assert;
import com.voc.service.model.api.IModelMilvusResultDataService;
import com.voc.service.model.model.VoiceClipRequestDataModel;
import com.voc.service.model.model.VoiceClipResultData;
import com.voc.service.model.utils.Milvus;
import com.voc.service.model.utils.OnnxRuntime;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * @创建者: fanrong
 * @创建时间: 2024/8/2 下午3:39
 * @描述:
 **/
@Service
public class ModelMilvusResultDataServiceImpl implements IModelMilvusResultDataService {
    private static final Logger log = LoggerFactory.getLogger(ModelMilvusResultDataServiceImpl.class);
    @Autowired
    private OrtSession session;
    @Autowired
    Milvus milvus;

    @Override
    public List<Float> findOnnxRuntimeEmbeddingData(VoiceClipRequestDataModel model) {
        if (ObjectUtils.isEmpty(model)) {
            log.warn("入参为空");
            return null;
        }
        // 设置onnxRuntime模型计算近似值时所需的参数
        List<String> texts = new ArrayList<>();
        if ("opinion".equalsIgnoreCase(model.getTag())) {
            StringBuffer buffer = new StringBuffer();
            if (ObjectUtils.isNotEmpty(model.getSubject()) && !"NA".equalsIgnoreCase(model.getSubject()) && !"null".equalsIgnoreCase(model.getSubject())) {
                buffer.append(model.getSubject());
            }
            if (ObjectUtils.isNotEmpty(model.getDesc()) && !"NA".equalsIgnoreCase(model.getDesc()) && !"null".equalsIgnoreCase(model.getDesc())) {
                buffer.append(model.getDesc());
            }
            texts.add(buffer.toString());
        } else if ("scenario".equalsIgnoreCase(model.getTag())) {
            if (ObjectUtils.isNotEmpty(model.getScenario()) && !"NA".equalsIgnoreCase(model.getScenario()) && !"null".equalsIgnoreCase(model.getScenario())) {
                texts.add(model.getScenario());
            }
        }

        if (ObjectUtils.isEmpty(texts)) {
            log.warn("onnxRuntime模型计算近似值时所需的参数被过滤为空,放弃本次近似值计算");
            return List.of();
        }
        OnnxRuntime runtime = new OnnxRuntime(session);
        long start = System.currentTimeMillis();
        //获取onnxRuntime生成embedding近似值
        final List<Float> embedding = runtime.getEmbedding(texts);
        long end = System.currentTimeMillis();
        log.info("onnxRuntime生成embedding耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end - start) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end - start) + "秒" : (end - start) + "毫秒");

        return embedding;
    }

    @Override
    public Map<String, List<Float>> getOnnxRuntimeEmbeddingData(List<String> opinionList) {
        if(ObjectUtils.isEmpty(opinionList)){
            log.warn("入参为空");
            return null;
        }
        OnnxRuntime runtime = new OnnxRuntime(session);
        Map<String, List<Float>> map = new ConcurrentHashMap<>();
        opinionList.stream().forEach(e->{
            final List<Float> embedding = runtime.getEmbedding(Arrays.asList(e));
            map.put(e,embedding);
        });
        return map;
    }

    @Override
    public List<VoiceClipResultData> findMilvusResultData(VoiceClipRequestDataModel model, List<Float> embeddingList) {
        Assert.isTrue(ObjectUtils.isNotEmpty(model), "入参不允许为空");
//        Assert.isTrue(ObjectUtils.isNotEmpty(embeddingList),"embedding不允许为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(model.getCollectionName()), "collectionName不允许为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(model.getFieldNames()), "fieldNames不允许为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(model.getVectorFieldName()), "vectorFieldName不允许为空");

        long start = System.currentTimeMillis();
        //查询向量库
        final List<VoiceClipResultData> search = milvus.search(embeddingList, model.getCollectionName(), model.getFieldNames(), model.getVectorFieldName(), model.getTopK());
        long end = System.currentTimeMillis();
        log.info("searchMilvus耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end - start) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end - start) + "秒" : (end - start) + "毫秒");
        return search;
    }

    @Override
    public void saveMilvusUnmatchedData(VoiceClipRequestDataModel model, List<Float> embeddingList, String clientId) {
        Assert.isTrue(ObjectUtils.isNotEmpty(clientId), "clientId不允许为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(model), "modelList不允许为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(embeddingList), "embeddingList不允许为空");
    }
}

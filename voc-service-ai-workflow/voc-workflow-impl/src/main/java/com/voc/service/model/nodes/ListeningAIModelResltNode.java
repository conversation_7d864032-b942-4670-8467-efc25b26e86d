package com.voc.service.model.nodes;

import cn.hutool.core.util.StrUtil;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.voc.service.model.producers.kafka.EventProducer;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "listeningAIModelResltNode", name = "监听第三方服务返回值节点")
public class ListeningAIModelResltNode extends AbstractNode {
    @Autowired
    EventProducer eventProducer;


    @Override
    public void process() throws Exception {
        try {
            ThirdPartyAIContext context = this.getRequestData();
            eventProducer.pushEvent(MessageDTO.builder().type(this.getTag()).data(context.getBatchId()).build());
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        ThirdPartyAIContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(this.getTag()), "getTag cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(context.getBatchId()), "getBatchNum cannot be empty");
//        Assert.isTrue(CollUtil.isNotEmpty(context.getIds()), "getIds cannot be empty");


        return true;
    }

}

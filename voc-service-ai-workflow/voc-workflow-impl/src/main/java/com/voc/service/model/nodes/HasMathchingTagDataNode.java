package com.voc.service.model.nodes;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.voc.service.model.nodes.abstracts.AbstractNodeIf;
import com.voc.service.model.nodes.context.ModelContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.RetryException;
import org.springframework.util.Assert;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "hasMatchingTagDataNode", name = "判断是否有相似查询结果节点")
public class HasMathchingTagDataNode extends AbstractNodeIf {
    private static final Logger log = LoggerFactory.getLogger(HasMathchingTagDataNode.class);

    @Override
    public boolean processIf() throws Exception {
        try {
            ModelContext context = this.getRequestData();
            if (CollUtil.isNotEmpty(context.getProcessingLabeledDataset())) {
                log.info("{} 存在相似查询结果", context.getTag());
                return true;
            }
            context.setNodeIdentifier("hasMatchingTagDataNode");
            log.info("{} 不存在相似查询结果", context.getTag());
        } catch (Exception e) {
            throw new RetryException(e.getMessage(), e);
        }
        return false;
    }

    @Override
    public boolean isAccess() {
        ModelContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getTag()), "getTag cannot be empty");
//        Assert.isTrue(CollUtil.isNotEmpty(context.getIds()), "getIds cannot be empty");


        return true;
    }

}

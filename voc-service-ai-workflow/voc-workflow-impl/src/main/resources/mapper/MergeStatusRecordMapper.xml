<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voc.service.model.mapper.MergeStatusRecordMapper">

    <select id="findUnpushedData" parameterType="com.voc.service.model.entity.MergeStatusRecordEntity" >
		select original_id from
		(
			select
				new_id,original_id, voice_id,
				label_num, if(label_count is null ,0 , label_count ) as label_count,
				scenario_num, if(scene_count is null ,0 , scene_count ) as scene_count,
				brand_car_num, if(series_count is null ,0 , series_count ) as series_count
			from (
				 select new_id,a.original_id, a.voice_id,
						a.label_num, b.label_count ,
						a.scenario_num , c.scene_count,
						a.brand_car_num , d.series_count
				 from (
					select new_id,original_id,voice_id, label_num , scenario_num,  brand_car_num from
					(select c.original_id as originalId  from
					(select count(*) as totalNum,original_id from mod_merge_status_record
					where done=0
					group by original_id
					) c
					left join
					(
					select count(*) as prossNum,original_id  from mod_merge_status_record
					where status =14 and done=0 and label_num &lt;&gt; -1 and scenario_num &lt;&gt; -1 and brand_car_num &lt;&gt; -1
					group by original_id
					) f
					on c.original_id=f.original_id where totalNum=prossNum limit 20) as  h
					left join
			      (select new_id,original_id,voice_id, label_num , scenario_num,  brand_car_num
					from mod_merge_status_record
					) as m
					on h.originalId=m.original_id
				  ) as a
				  left join
				  (
					  select sum(label_count) as label_count, voice_id ,original_id from (
							 select count(*) as label_count, voice_id ,original_id from mod_ftm_car_tag_result
							 where create_time &gt;= days_sub(CURRENT_DATE(),2 )
							 group by voice_id,original_id
							 union all
							 select count(*) as label_count, voice_id ,original_id from mod_ftm_car_not_tag_result
							 where create_time &gt;= days_sub(CURRENT_DATE(),2 )
							 group by voice_id,original_id
						 )a1 group by voice_id,original_id
				  ) as b on a.voice_id = b.voice_id and a.original_id = b.original_id
				  left join
				  (
					  select sum(scene_count) as scene_count, voice_id ,original_id from (
							 select count(*) as scene_count, voice_id ,original_id from mod_ftm_car_scene_result
							 where create_time &gt;= days_sub(CURRENT_DATE(),2 )
							 group by voice_id,original_id
							 union all
							 select count(*) as scene_count, voice_id ,original_id from mod_ftm_not_car_scene_result
							 where create_time &gt;= days_sub(CURRENT_DATE(),2 )
							 group by voice_id,original_id
						 )a1 group by voice_id,original_id
				  ) as c on a.voice_id = c.voice_id and a.original_id = c.original_id
				  left join
				  (
					  select count(*) as series_count, voice_id ,original_id from mod_ftm_brand_car_series
					  where create_time &gt;= days_sub(CURRENT_DATE(),2 )
					  group by voice_id,original_id
				  ) as d on a.voice_id = d.voice_id and a.original_id = d.original_id
			 )f1
		)f
		where label_num &lt;= label_count and scenario_num &lt;=scene_count and brand_car_num &lt;=series_count
		group by original_id
    </select>


	<select id="findCompletedData" parameterType="com.voc.service.model.entity.MergeStatusRecordEntity" >
		 select c.original_id as originalId  from
			(select count(*) as totalNum,original_id from mod_merge_status_record
				where done=0
				and  str_to_date(#{current_time}, '%Y-%m-%d %H:%i:%s') &gt;= seconds_add(update_time, 120)
				group by original_id
				limit #{limit}
			) c
		left join
		(
			select count(*) as prossNum,original_id  from mod_merge_status_record
			where status =14 and done=0
			  	and  str_to_date(#{current_time}, '%Y-%m-%d %H:%i:%s') &gt;= seconds_add(update_time, 120)
			group by original_id
		) f
		on c.original_id=f.original_id where totalNum=prossNum
	</select>

</mapper>
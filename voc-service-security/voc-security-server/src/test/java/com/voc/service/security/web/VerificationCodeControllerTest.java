package com.voc.service.security.web;

import com.service.login.BaseloginTest;
import com.voc.VocAuthServerApplication;
import com.voc.service.common.response.Result;
import com.voc.service.common.util.IdWorker;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName VerificationCodeControllerTest
 * @createTime 2024年01月15日 15:02
 * @Copyright futong
 */

public class VerificationCodeControllerTest  extends BaseloginTest {

    /**
     * GET请求样例
     */
    @Test
    void randomImage() {

        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("token", token);
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(params, requestHeaders);

        Result rs = restTemplate.postForEntity("/push"
                , requestEntity, Result.class).getBody();
        Assert.assertEquals("200", rs.getCode());
        System.out.println(rs);
    }
}

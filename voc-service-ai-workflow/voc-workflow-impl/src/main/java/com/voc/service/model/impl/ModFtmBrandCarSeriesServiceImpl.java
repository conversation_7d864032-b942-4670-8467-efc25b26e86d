package com.voc.service.model.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.model.api.IModFtmBrandCarSeriesService;
import com.voc.service.model.entity.ModFtmBrandCarSeriesEntity;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.ModFtmBrandCarSeriesMapper;
import com.voc.service.model.model.ModFtmBrandCarSeriesModel;
import com.voc.service.model.producers.kafka.ModelResultProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;


@Service
public class ModFtmBrandCarSeriesServiceImpl extends ServiceImpl<ModFtmBrandCarSeriesMapper, ModFtmBrandCarSeriesEntity>
        implements IModFtmBrandCarSeriesService {

    @Autowired
    ModelConvertMapperService modelConvertMapperService;

    @Autowired
    ModelResultProducer modelResultProducer;

    @Override
    public int saveModFtmBrandCarSeries(String clientId, List<ModFtmBrandCarSeriesModel> modelList) {
        if (CollectionUtil.isEmpty(modelList)) {
            return 0;
        }
        List<ModFtmBrandCarSeriesEntity> modFtmBrandCarSeriesEntities = modelConvertMapperService.cenvertToModFtmBrandCarSeriesEntityList(modelList);
        modelResultProducer.pushBrandCarData(MessageDTO.builder().source(clientId).data(modFtmBrandCarSeriesEntities).build());
        return modelList.size();
    }

    @Override
    public List<ModFtmBrandCarSeriesModel> findModFtmBrandCarSeries(Set<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return null;
        }
        List<ModFtmBrandCarSeriesEntity> modCarSceneResultEntities = this.list(new QueryWrapper<ModFtmBrandCarSeriesEntity>()
                .in("original_id", ids));
        return modelConvertMapperService.cenvertToModFtmBrandCarSeriesModeList(modCarSceneResultEntities);
    }
}





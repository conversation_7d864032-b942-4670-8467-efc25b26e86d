package com.voc.service.model.nodes.context;

import com.voc.service.model.model.AiBrandAndCarModel;
import com.voc.service.model.model.ModLlmReturnResultInfoRecordModel;
import com.voc.service.model.model.ModWorkflowModel;
import com.voc.service.model.nodes.abstracts.AbstractContext;
import com.voc.service.model.vo.BrandCarVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.*;

/**
 * @Title: AiWorkflowDefaultContext
 * @Package: com.voc.service.model.nodes.context
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 14:28
 * @Version:1.0
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
public class AIResultDataContext extends AbstractContext implements Serializable {


    String workId;
    @Schema(description = "客户ID")
    String clientId;
    @Schema(description = "内容ID")
    String originalId;

    @Schema(description = "渠道Id")
    String channelId;
    @Schema(description = "文章类型")
    String contentType;
    @Schema(description = "模型类型")
    Integer modelType;
    @Schema(description = "请求AI品牌车系数据")
    List<AiBrandAndCarModel> aiBrandAndCarModelList;
    @Builder.Default
    List<ModLlmReturnResultInfoRecordModel> brandCarDataList = new ArrayList<>();
    @Builder.Default
    List<ModLlmReturnResultInfoRecordModel> opinionDataList = new ArrayList<>();
    @Builder.Default
    List<ModLlmReturnResultInfoRecordModel> scenarioDataList = new ArrayList<>();
    @Builder.Default
    Map<String, List<ModLlmReturnResultInfoRecordModel>> filteredRequiredFieldsMap = new HashMap();
    @Builder.Default
    Map<Integer, List<ModWorkflowModel>> filterMap = new HashMap();
    //小模型返回的品牌车系数据
    @Builder.Default
    List<BrandCarVo> brandCarVoList = new ArrayList<>();
    //三条线状态map<"2\4\8",声音ID>
    @Builder.Default
    Set<String> ids = new HashSet<>();
}

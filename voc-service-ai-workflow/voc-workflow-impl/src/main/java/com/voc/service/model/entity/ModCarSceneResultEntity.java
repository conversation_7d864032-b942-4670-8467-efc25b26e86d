package com.voc.service.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Title: ModCarSceneResultEntity
 * @Package: com.voc.service.model.entity
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/6 10:20
 * @Version:1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "mod_ftm_car_scene_result")
public class ModCarSceneResultEntity  implements Serializable {
    String newId;
    String workId;
    String voiceId;
    String originalId;
    String simScenario;
    String score;
    String scenarioSchema;
    LocalDateTime createTime;
    int done;
    private String channelId;
    private String contentType;
    private Integer modelType;
    private Object extFields;

}

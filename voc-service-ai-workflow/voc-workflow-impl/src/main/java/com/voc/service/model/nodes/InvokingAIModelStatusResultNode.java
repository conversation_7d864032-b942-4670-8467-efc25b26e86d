package com.voc.service.model.nodes;

import com.voc.service.model.api.IModLlmBatchInfoRecordService;
import com.voc.service.model.nodes.abstracts.AbstractNode;
import com.voc.service.model.nodes.context.ThirdPartyAIContext;
import com.voc.service.trhird.api.ZhiPuAiApi;
import com.voc.service.trhird.model.ZhiPuStatusAiModel;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/29 15:34
 * @Version:1.0
 */
@LiteflowComponent(id = "invokingAIModelStatusResultNode", name = "根据批次号调用模型获取执行结果节点")
public class InvokingAIModelStatusResultNode extends AbstractNode {

    private static final Logger log = LoggerFactory.getLogger(InvokingAIModelStatusResultNode.class);
    @Autowired
    ZhiPuAiApi zhiPuAiApi;

    @Autowired
    IModLlmBatchInfoRecordService modLlmBatchInfoRecordService;

    @Override
    public void process() throws Exception {
        try {
            ThirdPartyAIContext context = this.getRequestData();
            String batchId = context.getBatchId();
            Assert.isTrue(StringUtils.isNotEmpty(batchId), "batch id cannot be empty");
            try {
                ZhiPuStatusAiModel model = zhiPuAiApi.batchesRetrieveByBatchId(batchId);
                if (ObjectUtils.isEmpty(model)) {
                    log.error("调用智谱AI查询状态接口为空");
                }
                log.info("调用智谱AI查询状态接口:{}", model);
                int batchStatus = modLlmBatchInfoRecordService.modifyAIModelBatchStatus(model.getBatchId(), model.getStatus(), model.getOutputFileId(), model.getErrorFileId());
                log.info("调用批次状态结果:{}", batchStatus);
                context.setZhiPuStatusAiModel(model);
            } catch (Exception e) {
                log.error("调用智谱AI查询状态接口异常:", e);
            }
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        return true;
    }

}

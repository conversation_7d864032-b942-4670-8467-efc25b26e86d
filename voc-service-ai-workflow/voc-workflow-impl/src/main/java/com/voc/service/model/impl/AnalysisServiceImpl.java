package com.voc.service.model.impl;

import cn.hutool.core.collection.CollUtil;
import com.voc.service.model.api.IAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Title: AnalysisServiceImpl
 * @Package: com.voc.service.model.impl
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 17:17
 * @Version:1.0
 */
@Service
public class AnalysisServiceImpl implements IAnalysisService {
    @Autowired
    RedisTemplate redisTemplate;

    static final String key_s = "ai-workflow".concat("::push_analysis::");
    static final String key_status = "ai-workflow".concat("::status_analysis::");


    @Override
    public void markDataStatus(Set<String> ids, long period) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        final Map<String, String> map = ids.parallelStream().map(id -> key_s.concat(id))
                .collect(Collectors.toMap(id -> id, id -> "1"));
        redisTemplate.opsForValue().multiSet(map);
        // 为每个键设置过期时间
        map.keySet().forEach(key -> redisTemplate.expire(key, 24,  TimeUnit.HOURS));
    }
    @Override
    public void removeMarkDataStatus(Set<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        final Set<String> fIds = ids.parallelStream().map(id -> key_s.concat(id)).collect(Collectors.toSet());
        redisTemplate.delete(fIds);
    }
    @Override
    public Set<String> effectiveDataset(Set<String> ids) {
        return ids.parallelStream().filter(id -> !redisTemplate.hasKey(key_s.concat(id))).collect(Collectors.toSet());

//        return new HashSet<>(Arrays.asList("1", "2"));
    }


    @Override
    public void removeDataStatus(Set<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        final Set<String> fIds = ids.parallelStream().map(id -> key_status.concat(id)).collect(Collectors.toSet());
        redisTemplate.delete(fIds);
    }





    /*@Override
    public Set<String> readAllUnprocessedIds() {
        //读取所有运行中的keys
        final Set<String> keys = redisTemplate.keys(key_status.concat("*"));
        final Set<String> runningids = keys.stream().map(key -> StrUtil.replace(key, key_status, "")).collect(Collectors.toSet());

        return runningids;
    }*/

    /*@Override
    public boolean isExistUnprocessedDataset(String id) {
        if (StrUtil.isNotBlank(id)) {
            return redisTemplate.hasKey(key_status.concat(id));
        }
        return false;
    }*/


}

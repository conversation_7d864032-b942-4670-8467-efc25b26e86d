package com.voc.service.model.impl;

import com.voc.service.model.api.IMessageQueueService;
import com.voc.service.model.listener.KafkaListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * @Title: KafkaServiceImpl
 * @Package: com.voc.service.model.impl
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/22 15:22
 * @Version:1.0
 */
@Service
public class KafkaServiceImpl implements IMessageQueueService {
    @Autowired
    KafkaListener kafkaListener;

    public void stopListener(Set<String> topics) {
        kafkaListener.stopListener(topics);
    }

    @Override
    public void startListener(Set<String> topics) {
        kafkaListener.startListener(topics);
    }

    @Override
    public boolean isListenerRunning(String topic) {
        return kafkaListener.isListenerRunning(topic);
    }
}

package com.voc.service.model.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.model.api.IModCaTagResultService;
import com.voc.service.model.entity.ModCaTagResultEntity;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.ModCaTagResultMapper;
import com.voc.service.model.model.ModCaTagResultModel;
import com.voc.service.model.producers.kafka.ModelResultProducer;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Title: ModCaTagResultServiceImpl
 * @Package: com.voc.service.model.impl
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/6 10:14
 * @Version:1.0
 */
@Service
public class ModCaTagResultServiceImpl extends ServiceImpl<ModCaTagResultMapper, ModCaTagResultEntity>
        implements IModCaTagResultService {

    @Autowired
    ModelConvertMapperService modelConvertMapperService;

    @Autowired
    ModelResultProducer modelResultProducer;

    @Override
    public boolean add(String clientId, List<ModCaTagResultModel> model) {
        if (CollUtil.isEmpty(model)) {
            return false;
        }
        List<ModCaTagResultEntity> entity = model.stream().map(e -> {
            ModCaTagResultEntity entitys = new ModCaTagResultEntity();
            BeanUtils.copyProperties(e, entitys);
            return entitys;
        }).collect(Collectors.toList());
//        List<ModCaTagResultEntity> entity = modelConvertMapperService.cenvertToModCaTagResultEntityList(model);
        modelResultProducer.pushCarTagResultData(MessageDTO.builder().source(clientId).data(entity).build());
        return true;
    }

    @Override
    public List<ModCaTagResultModel> findTagResultModel(Set<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return null;
        }
        List<ModCaTagResultEntity> modCaTagResultEntities = this.list(new QueryWrapper<ModCaTagResultEntity>()
                .in("original_id", ids));
        List<ModCaTagResultModel> collect = modCaTagResultEntities.stream().map(e -> {
            ModCaTagResultModel model = new ModCaTagResultModel();
            BeanUtils.copyProperties(e, model);
            return model;
        }).collect(Collectors.toList());
//        return modelConvertMapperService.cenvertToModCaTagResultModelList(modCaTagResultEntities);
        return collect;
    }
}

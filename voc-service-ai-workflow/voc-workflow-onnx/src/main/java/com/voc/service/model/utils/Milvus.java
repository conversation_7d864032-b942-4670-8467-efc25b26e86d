package com.voc.service.model.utils;

import cn.hutool.core.collection.CollUtil;
import com.voc.service.common.util.ServiceContextHolder;
import com.voc.service.model.model.VoiceClipResultData;
import io.milvus.client.MilvusServiceClient;
import io.milvus.common.clientenum.ConsistencyLevelEnum;
import io.milvus.grpc.SearchResults;
import io.milvus.param.R;
import io.milvus.param.dml.InsertParam;
import io.milvus.param.dml.SearchParam;
import io.milvus.response.QueryResultsWrapper;
import io.milvus.response.SearchResultsWrapper;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @创建者: fanrong
 * @创建时间: 2024/8/2 下午3:24
 * @描述:
 **/
@Component
public class Milvus {
    private static final Logger log = LoggerFactory.getLogger(Milvus.class);
    @Autowired
    MilvusServiceClient milvusServiceClient;

    /**
     * @param list            onnxRuntime模型计算的近似值结果[embedding]
     * @param collectionName  向量库表名称
     * @param fieldNames      向量库结果返回字段
     * @param vectorFieldName 向量库查询字段 等同于mysql 的where条件
     * @param topK            限制条数 等同于mysql 的limit
     * @return java.util.List<com.voc.service.model.model.VoiceClipResultData>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/8/9 下午1:31
     * @描述
     **/
    public List<VoiceClipResultData> search(List<Float> list, String collectionName, List<String> fieldNames, String vectorFieldName, Integer topK) {
        if (ObjectUtils.isEmpty(list)) {
            log.debug("Embedding 值为空,放弃本次查询");
            return List.of();
        }
        Assert.isTrue(ObjectUtils.isNotEmpty(vectorFieldName), "vectorFieldName不允许为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(collectionName), "collectionName不允许为空");
        //构建向量库查询对象
        SearchParam.Builder builder = SearchParam.newBuilder()
                .withCollectionName(collectionName)
                .withTopK(topK)
                .withVectors(Arrays.asList(list))
                .withConsistencyLevel(ConsistencyLevelEnum.BOUNDED)
                .withVectorFieldName(vectorFieldName);
        if (ObjectUtils.isNotEmpty(fieldNames)) {
            builder.withOutFields(fieldNames);
        }
        if (ObjectUtils.isEmpty(this.milvusServiceClient)) {
            this.milvusServiceClient = ServiceContextHolder.getApplicationContext().getBean(MilvusServiceClient.class);
        }
        long start = System.currentTimeMillis();

        R<SearchResults> search = milvusServiceClient.search(
                builder.build()
        );


//        ListenableFuture<R<SearchResults>> rListenableFuture = milvusServiceClient.searchAsync(builder.build());
//        R<SearchResults> search = null;
//        try {
//            search = rListenableFuture.get();
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        } catch (ExecutionException e) {
//            throw new RuntimeException(e);
//        }
        long end = System.currentTimeMillis();
//        log.info("异步查询向量库耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end-start)>0?TimeUnit.MILLISECONDS.toSeconds(end-start)+"秒":(end-start)+"毫秒");
        log.info("同步查询向量库耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end - start) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end - start) + "秒" : (end - start) + "毫秒");
        if (ObjectUtils.isEmpty(search.getData())) {
            return List.of();
        }
        //获取向量库查询结果并转换为VoiceClipResultData
        SearchResultsWrapper wrapper = new SearchResultsWrapper(search.getData().getResults());
        if (ObjectUtils.isNotEmpty(wrapper) && CollUtil.isNotEmpty(wrapper.getRowRecords())) {
            QueryResultsWrapper.RowRecord rowRecord = wrapper.getRowRecords().get(0);
            log.info("获取score的数据:{}", rowRecord.getFieldValues());
        }
        List<VoiceClipResultData> collect = wrapper.getRowRecords().stream().map(e -> {
            Map<String, Object> fieldValues = e.getFieldValues();
            VoiceClipResultData build = VoiceClipResultData.builder()
                    .id(fieldValues.containsKey("id") ? String.valueOf(fieldValues.get("id")) : null)
                    .score(fieldValues.containsKey("score") ? Float.valueOf(String.valueOf(fieldValues.get("score"))) : null)
                    .topic(fieldValues.containsKey("topic") ? String.valueOf(fieldValues.get("topic")) : null)
                    .sim_opinion(fieldValues.containsKey("opinion") ? String.valueOf(fieldValues.get("opinion")) : null)
                    .tagsLevel4(fieldValues.containsKey("L4") ? String.valueOf(fieldValues.get("L4")) : null)
                    .build();
            return build;
        }).collect(Collectors.toList());

        return collect;
    }

    public void saveMilvusData(String opinion, List<Float> embeddingList, String clientId, String collectionName) {
        List<InsertParam.Field> fields = new ArrayList<>();
        fields.add(new InsertParam.Field("opinion", Arrays.asList(opinion)));
        fields.add(new InsertParam.Field("embedding", embeddingList));
        fields.add(new InsertParam.Field("clientId", Arrays.asList(clientId)));

        InsertParam insertParam = InsertParam.newBuilder().withCollectionName(collectionName).withFields(fields).build();
        if (ObjectUtils.isEmpty(this.milvusServiceClient)) {
            this.milvusServiceClient = ServiceContextHolder.getApplicationContext().getBean(MilvusServiceClient.class);
        }

        milvusServiceClient.insert(insertParam);

    }

}

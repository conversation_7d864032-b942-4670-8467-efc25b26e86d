package com.voc.service.model.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.common.util.ServiceContextHolder;
import com.voc.service.model.api.ICarAndDimOpinionMappingService;
import com.voc.service.model.entity.CarAndDimOpinionMappingEntity;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.CarAndDimOpinionMappingMapper;
import com.voc.service.model.model.CarAndDimOpinionMappingModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ConcurrentReferenceHashMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @创建者: fanrong
 * @创建时间: 2024/8/6 下午2:48
 * @描述:
 **/
@Service
public class CarAndDimOpinionMappingServiceImpl extends ServiceImpl<CarAndDimOpinionMappingMapper, CarAndDimOpinionMappingEntity>
        implements ICarAndDimOpinionMappingService {
    @Autowired
    ModelConvertMapperService convertMapperService;

    @CreateCache(area = "VDP", name = ":",  cacheType = CacheType.REMOTE)
    private Cache<String, Map<String, List<CarAndDimOpinionMappingModel>>> opinionMappingCache;
    private static final String OPINION_MAPPING_MAPPING_KEY = "{}:opinion_mapping:{}";

    private String getOpinionMappingCacheKey(Object... params){
        return StrUtil.format(OPINION_MAPPING_MAPPING_KEY, ServiceContextHolder.getSystemId(),params);
    }


    @Override
    public Map<String, List<CarAndDimOpinionMappingModel>> findAllCarAndDimOpinionMapping() {

        Map<String, List<CarAndDimOpinionMappingModel>> map = opinionMappingCache.get(this.getOpinionMappingCacheKey("0"));
        if (CollUtil.isEmpty(map)) {
            List<CarAndDimOpinionMappingEntity> carAndDimOpinionMappingEntities = this.baseMapper.selectList(new QueryWrapper<>());
            List<CarAndDimOpinionMappingModel> carAndDimOpinionMappingModels = convertMapperService.cenvertToCarAndDimOpinionMappingModelList(carAndDimOpinionMappingEntities);
//            Map<String, List<CarAndDimOpinionMappingModel>> collect = carAndDimOpinionMappingModels.stream().collect(Collectors.groupingBy(CarAndDimOpinionMappingModel::getOpinion));

            Map<String, List<CarAndDimOpinionMappingModel>> maps = new ConcurrentReferenceHashMap<>();

            carAndDimOpinionMappingModels.stream()
                    .map(model -> {
                        model.setMd5(this.getMd5(model.getOpinion()));
                        return model;
                    })
                    .forEach(model -> {
                        if (maps.containsKey(model.getMd5())) {
                            maps.get(model.getMd5()).add(model);
                        } else {
                            maps.put(model.getMd5(), new ArrayList<>(Arrays.asList(model)));
                        }
                    });
            map = maps;
            opinionMappingCache.put(this.getOpinionMappingCacheKey("0"), map);
        }

        return map;
    }
    /*public Map<String, List<CarAndDimOpinionMappingModel>> findAllCarAndDimOpinionMapping2() {
        return cache.computeIfAbsent("all",(key)->{
            List<CarAndDimOpinionMappingEntity> carAndDimOpinionMappingEntities = this.baseMapper.selectList(new QueryWrapper<>());
            List<CarAndDimOpinionMappingModel> carAndDimOpinionMappingModels = convertMapperService.cenvertToCarAndDimOpinionMappingModelList(carAndDimOpinionMappingEntities);
            Map<String, List<CarAndDimOpinionMappingModel>> collect = carAndDimOpinionMappingModels.stream().collect(Collectors.groupingBy(CarAndDimOpinionMappingModel::getOpinion));
            Map<String, List<CarAndDimOpinionMappingModel>> md5KeyMap = new HashMap<>();
            collect.entrySet().stream().forEach(e->{
                md5KeyMap.put(DigestUtil.md5Hex(e.getKey()),e.getValue());
            });
            return md5KeyMap;
        });
    }*/

    @Override
    public String getMd5(String opinion) {
        return DigestUtil.md5Hex(opinion);
    }
}

package com.voc.service.model.producers.kafka;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.analysis.dto.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CatListener
 * @createTime 2024年03月14日 9:46
 * @Copyright cuick
 */
@Component("unprocessedResultData.producer.kafka")
public class UnprocessedResultDataProducer {
    public static final String TOPIC_DATA = "VDP_unprocessedResultData_push";
    public static final String TOPIC_DATA_HIGHT_LEVEL = "VDP_unprocessedResultDataHightLevel_push";
    @Autowired
    KafkaTemplate<String, String> kafkaTemplate;

    public void pushData(MessageDTO msg) {
        Assert.isTrue(ObjUtil.isNotEmpty(msg.getData()), "getData cannot be empty");
        final MessageExt hightLevelExt = Optional.ofNullable(msg.getExt().stream().filter(ext -> ext.getKey().equals("hightLevel")).findFirst().get())
                .orElse(MessageExt.builder().key("hightLevel").value("false").build());
        final String sendText = JSONUtil.toJsonStr(msg, JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
        //高优先级  -  根据 hightLevel 值，分发到不同的topic
        if (Boolean.TRUE.booleanValue() == BooleanUtil.toBoolean(String.valueOf(hightLevelExt.getValue()))) {
            kafkaTemplate.send(TOPIC_DATA_HIGHT_LEVEL, sendText);
        } else {
            kafkaTemplate.send(TOPIC_DATA, sendText);
        }
    }

}


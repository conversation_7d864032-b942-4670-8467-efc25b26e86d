spring.application.name: springbootadmin


spring.cloud.nacos.config.server-addr: ${NACOS_SERVER_ADDR_URLS:nacos-cluster-hs.pz1a-prod.svc:8848}
spring.cloud.nacos.config.file-extension: yml
spring.cloud.nacos.config.group: ${NACOS_CONFIG_GROUP:DEFAULT_GROUP}
spring.cloud.nacos.config.namespace: ${NACOS_CONFIG_NAMESPACE:85a785c4-b2c1-4beb-a51d-0a0b0980558a}
spring.cloud.nacos.config.username: ${NACOS_CONFIG_USER_NAME:dndc-vdp-voc}
spring.cloud.nacos.config.password: ${NACOS_CONFIG_PASSWORD:PBE(vGKr29OfTMRncMpXdjdc/S8ytbdSQb2HYFxqWyMsinbOr+vZUiuOS0Nr4G+lV5g0Xxlg5r5X1s72C1M3fs+wcQ==)}
spring.cloud.nacos.config.extension-configs[0].data-id: common-nacos.yml

spring.cloud.loadbalancer.ribbon.enabled: false
spring.cloud.nacos.config.enabled: ${NACOS_CONFIG_ENABLED:true}
spring.cloud.nacos.discovery.enabled: ${NACOS_DISCOVERY_ENABLED:true}

spring:
  security:
    user:
      #admin Server端登录时用的账户密码
      name: ${USERNAME:admin}
      password: ${PASSWORD:Passw0rd}